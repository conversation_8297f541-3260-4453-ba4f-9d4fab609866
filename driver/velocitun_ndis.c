/* SPDX-License-Identifier: GPL-2.0
 *
 * Copyright (C) 2024 VelociTun Team. All Rights Reserved.
 * NDIS-specific functions for VelociTun driver.
 */

#include <ntifs.h>
#include <wdm.h>
#include <wdmsec.h>
#include <ndis.h>
#include <ntstrsafe.h>
#include "undocumented.h"
#include "velocitun.h"

// External declarations
extern ERESOURCE VelociTunDispatchCtxGuard, VelociTunDispatchDeviceListLock;
extern RTL_STATIC_LIST_HEAD(VelociTunDispatchDeviceList);

// Function declarations from velocitun.c
VOID VelociTunUnregisterBuffers(_Inout_ VELOCITUN_CTX *Ctx, _In_ FILE_OBJECT *Owner);
NTSTATUS VelociTunRegisterBuffers(_Inout_ VELOCITUN_CTX *Ctx, _Inout_ IRP *Irp);
VOID VelociTunProcessNotification(HANDLE ParentId, HANDLE ProcessId, BOOLEAN Create);
NTSTATUS VelociTunDispatchDeviceControl(DEVICE_OBJECT *DeviceObject, IRP *Irp);
NTSTATUS VelociTunDispatchClose(DEVICE_OBJECT *DeviceObject, IRP *Irp);

#define VELOCITUN_FORCE_UNREGISTRATION ((FILE_OBJECT *)-1)

// Global variables
UINT NdisVersion;
NDIS_HANDLE NdisMiniportDriverHandle;
DRIVER_DISPATCH *NdisDispatchDeviceControl, *NdisDispatchClose, *NdisDispatchPnp;

_Dispatch_type_(IRP_MJ_PNP) static DRIVER_DISPATCH_PAGED VelociTunDispatchPnp;
_Use_decl_annotations_ static NTSTATUS
VelociTunDispatchPnp(DEVICE_OBJECT *DeviceObject, IRP *Irp)
{
    IO_STACK_LOCATION *Stack = IoGetCurrentIrpStackLocation(Irp);
    if (Stack->MinorFunction != IRP_MN_QUERY_REMOVE_DEVICE && Stack->MinorFunction != IRP_MN_SURPRISE_REMOVAL)
        goto ndisDispatch;

#pragma warning(suppress : 28175)
    VELOCITUN_CTX *Ctx = DeviceObject->Reserved;
    if (!Ctx)
        goto ndisDispatch;

    ExAcquireResourceExclusiveLite(&Ctx->Device.RegistrationLock, TRUE);
    if (!Ctx->Device.OwningFileObject || Ctx->Device.OwningFileObject == Stack->FileObject)
        goto cleanupLock;

    NTSTATUS Status;
    PEPROCESS Process;
    KAPC_STATE ApcState;
    PVOID Object = NULL;
    OBJECT_HANDLE_INFORMATION HandleInfo;
    SYSTEM_HANDLE_INFORMATION_EX *HandleTable = NULL;
    ULONG VerifierFlags = 0;

    for (ULONG Size = 0, RequestedSize;
         (Status = ZwQuerySystemInformation(SystemExtendedHandleInformation, HandleTable, Size, &RequestedSize)) ==
         STATUS_INFO_LENGTH_MISMATCH;
         Size = RequestedSize)
    {
        if (HandleTable)
            ExFreePoolWithTag(HandleTable, VELOCITUN_MEMORY_TAG);
        HandleTable = ExAllocatePoolUninitialized(PagedPool, RequestedSize, VELOCITUN_MEMORY_TAG);
        if (!HandleTable)
            break;
    }
    if (!NT_SUCCESS(Status) || !HandleTable)
        goto cleanupHandleTable;

    MmIsVerifierEnabled(&VerifierFlags);

    for (ULONG_PTR Index = 0; Index < HandleTable->NumberOfHandles; ++Index)
    {
        FILE_OBJECT *FileObject = HandleTable->Handles[Index].Object;
        if (FileObject != Ctx->Device.OwningFileObject)
            continue;
        Status = PsLookupProcessByProcessId(HandleTable->Handles[Index].UniqueProcessId, &Process);
        if (!NT_SUCCESS(Status))
            continue;
        KeStackAttachProcess(Process, &ApcState);
        if (!VerifierFlags)
#pragma warning(suppress : 28126)
            Status = ObReferenceObjectByHandle(
                HandleTable->Handles[Index].HandleValue, 0, NULL, UserMode, &Object, &HandleInfo);
        if (NT_SUCCESS(Status))
        {
            if (VerifierFlags || Object == FileObject)
                ObCloseHandle(HandleTable->Handles[Index].HandleValue, UserMode);
            if (!VerifierFlags)
                ObfDereferenceObject(Object);
        }
        KeUnstackDetachProcess(&ApcState);
        ObfDereferenceObject(Process);
    }
cleanupHandleTable:
    if (HandleTable)
        ExFreePoolWithTag(HandleTable, VELOCITUN_MEMORY_TAG);
cleanupLock:
    ExReleaseResourceLite(&Ctx->Device.RegistrationLock);
ndisDispatch:
    return NdisDispatchPnp(DeviceObject, Irp);
}

static MINIPORT_RESTART VelociTunRestart;
_Use_decl_annotations_ static NDIS_STATUS
VelociTunRestart(NDIS_HANDLE MiniportAdapterContext, PNDIS_MINIPORT_RESTART_PARAMETERS MiniportRestartParameters)
{
    VELOCITUN_CTX *Ctx = (VELOCITUN_CTX *)MiniportAdapterContext;
    WriteRelease(&Ctx->Running, TRUE);
    return NDIS_STATUS_SUCCESS;
}

static MINIPORT_PAUSE VelociTunPause;
_Use_decl_annotations_ static NDIS_STATUS
VelociTunPause(NDIS_HANDLE MiniportAdapterContext, PNDIS_MINIPORT_PAUSE_PARAMETERS MiniportPauseParameters)
{
    VELOCITUN_CTX *Ctx = (VELOCITUN_CTX *)MiniportAdapterContext;

    WriteRelease(&Ctx->Running, FALSE);
    ExReleaseSpinLockExclusive(
        &Ctx->TransitionLock,
        ExAcquireSpinLockExclusive(&Ctx->TransitionLock)); /* Ensure above change is visible to all readers. */

    KeWaitForSingleObject(&Ctx->Device.Receive.ActiveNbls.Empty, Executive, KernelMode, FALSE, NULL);

    return NDIS_STATUS_SUCCESS;
}

static MINIPORT_DEVICE_PNP_EVENT_NOTIFY VelociTunDevicePnPEventNotify;
_Use_decl_annotations_ static VOID
VelociTunDevicePnPEventNotify(NDIS_HANDLE MiniportAdapterContext, PNET_DEVICE_PNP_EVENT NetDevicePnPEvent)
{
}

static MINIPORT_INITIALIZE VelociTunInitializeEx;
_Use_decl_annotations_ static NDIS_STATUS
VelociTunInitializeEx(
    NDIS_HANDLE MiniportAdapterHandle,
    NDIS_HANDLE MiniportDriverContext,
    PNDIS_MINIPORT_INIT_PARAMETERS MiniportInitParameters)
{
    NDIS_STATUS Status;

    if (!MiniportAdapterHandle)
        return NDIS_STATUS_FAILURE;

/* Leaking memory 'Ctx'. Note: 'Ctx' is freed in VelociTunHaltEx or on failure. */
#pragma warning(suppress : 6014)
#pragma warning(suppress : 28160)
    VELOCITUN_CTX *Ctx = ExAllocatePoolZero(NonPagedPool, sizeof(*Ctx), VELOCITUN_MEMORY_TAG);
    if (!Ctx)
        return NDIS_STATUS_FAILURE;

    Ctx->MiniportAdapterHandle = MiniportAdapterHandle;

    NdisMGetDeviceProperty(MiniportAdapterHandle, NULL, &Ctx->FunctionalDeviceObject, NULL, NULL, NULL);
    if (Status = NDIS_STATUS_FAILURE, !Ctx->FunctionalDeviceObject)
        goto cleanupFreeCtx;
#pragma warning(suppress : 28175)
    ASSERT(!Ctx->FunctionalDeviceObject->Reserved);
    /* Reverse engineering indicates that we'd be better off calling
     * NdisWdfGetAdapterContextFromAdapterHandle(functional_device),
     * which points to our VELOCITUN_CTX object directly, but this isn't
     * available before Windows 10, so for now we just stick it into
     * this reserved field. Revisit this when we drop support for old
     * Windows versions. */
#pragma warning(suppress : 28175)
    Ctx->FunctionalDeviceObject->Reserved = Ctx;

    Ctx->Statistics.Header.Type = NDIS_OBJECT_TYPE_DEFAULT;
    Ctx->Statistics.Header.Revision = NDIS_STATISTICS_INFO_REVISION_1;
    Ctx->Statistics.Header.Size = NDIS_SIZEOF_STATISTICS_INFO_REVISION_1;
    Ctx->Statistics.SupportedStatistics =
        NDIS_STATISTICS_FLAGS_VALID_DIRECTED_FRAMES_RCV | NDIS_STATISTICS_FLAGS_VALID_MULTICAST_FRAMES_RCV |
        NDIS_STATISTICS_FLAGS_VALID_BROADCAST_FRAMES_RCV | NDIS_STATISTICS_FLAGS_VALID_BYTES_RCV |
        NDIS_STATISTICS_FLAGS_VALID_RCV_DISCARDS | NDIS_STATISTICS_FLAGS_VALID_RCV_ERROR |
        NDIS_STATISTICS_FLAGS_VALID_DIRECTED_FRAMES_XMIT | NDIS_STATISTICS_FLAGS_VALID_MULTICAST_FRAMES_XMIT |
        NDIS_STATISTICS_FLAGS_VALID_BROADCAST_FRAMES_XMIT | NDIS_STATISTICS_FLAGS_VALID_BYTES_XMIT |
        NDIS_STATISTICS_FLAGS_VALID_XMIT_ERROR | NDIS_STATISTICS_FLAGS_VALID_XMIT_DISCARDS |
        NDIS_STATISTICS_FLAGS_VALID_DIRECTED_BYTES_RCV | NDIS_STATISTICS_FLAGS_VALID_MULTICAST_BYTES_RCV |
        NDIS_STATISTICS_FLAGS_VALID_BROADCAST_BYTES_RCV | NDIS_STATISTICS_FLAGS_VALID_DIRECTED_BYTES_XMIT |
        NDIS_STATISTICS_FLAGS_VALID_MULTICAST_BYTES_XMIT | NDIS_STATISTICS_FLAGS_VALID_BROADCAST_BYTES_XMIT;
    KeInitializeEvent(&Ctx->Device.Disconnected, NotificationEvent, TRUE);
    KeInitializeSpinLock(&Ctx->Device.Send.Lock);
    KeInitializeSpinLock(&Ctx->Device.Receive.Lock);
    KeInitializeEvent(&Ctx->Device.Receive.ActiveNbls.Empty, NotificationEvent, TRUE);
    ExInitializeResourceLite(&Ctx->Device.RegistrationLock);

    NET_BUFFER_LIST_POOL_PARAMETERS NblPoolParameters = {
        .Header = {.Type = NDIS_OBJECT_TYPE_DEFAULT,
                   .Revision = NET_BUFFER_LIST_POOL_PARAMETERS_REVISION_1,
                   .Size = NDIS_SIZEOF_NET_BUFFER_LIST_POOL_PARAMETERS_REVISION_1},
        .ProtocolId = NDIS_PROTOCOL_ID_DEFAULT,
        .fAllocateNetBuffer = TRUE,
        .PoolTag = VELOCITUN_MEMORY_TAG};
/* Leaking memory 'Ctx->NblPool'. Note: 'Ctx->NblPool' is freed in VelociTunHaltEx or on failure. */
#pragma warning(suppress : 6014)
    Ctx->NblPool = NdisAllocateNetBufferListPool(MiniportAdapterHandle, &NblPoolParameters);
    if (Status = NDIS_STATUS_FAILURE, !Ctx->NblPool)
        goto cleanupFreeCtx;

    NDIS_MINIPORT_ADAPTER_REGISTRATION_ATTRIBUTES AdapterRegistrationAttributes = {
        .Header = {.Type = NDIS_OBJECT_TYPE_MINIPORT_ADAPTER_REGISTRATION_ATTRIBUTES,
                   .Revision = NdisVersion < NDIS_RUNTIME_VERSION_630
                                   ? NDIS_MINIPORT_ADAPTER_REGISTRATION_ATTRIBUTES_REVISION_1
                                   : NDIS_MINIPORT_ADAPTER_REGISTRATION_ATTRIBUTES_REVISION_2,
                   .Size = NdisVersion < NDIS_RUNTIME_VERSION_630
                               ? NDIS_SIZEOF_MINIPORT_ADAPTER_REGISTRATION_ATTRIBUTES_REVISION_1
                               : NDIS_SIZEOF_MINIPORT_ADAPTER_REGISTRATION_ATTRIBUTES_REVISION_2},
        .AttributeFlags = NDIS_MINIPORT_ATTRIBUTES_NO_HALT_ON_SUSPEND | NDIS_MINIPORT_ATTRIBUTES_SURPRISE_REMOVE_OK,
        .InterfaceType = NdisInterfaceInternal,
        .MiniportAdapterContext = Ctx};
    if (Status = NDIS_STATUS_FAILURE,
        !NT_SUCCESS(NdisMSetMiniportAttributes(
            MiniportAdapterHandle, (PNDIS_MINIPORT_ADAPTER_ATTRIBUTES)&AdapterRegistrationAttributes)))
        goto cleanupFreeNblPool;

    // Power management capabilities - simplified for compatibility
    NDIS_PNP_CAPABILITIES PnpCapabilities = {
        .WakeUpCapabilities = {
            .MinMagicPacketWakeUp = NdisDeviceStateUnspecified,
            .MinPatternWakeUp = NdisDeviceStateUnspecified,
            .MinLinkChangeWakeUp = NdisDeviceStateUnspecified}};
    static NDIS_OID SupportedOids[] = {OID_GEN_MAXIMUM_TOTAL_SIZE,
                                       OID_GEN_CURRENT_LOOKAHEAD,
                                       OID_GEN_TRANSMIT_BUFFER_SPACE,
                                       OID_GEN_RECEIVE_BUFFER_SPACE,
                                       OID_GEN_TRANSMIT_BLOCK_SIZE,
                                       OID_GEN_RECEIVE_BLOCK_SIZE,
                                       OID_GEN_VENDOR_DESCRIPTION,
                                       OID_GEN_VENDOR_ID,
                                       OID_GEN_VENDOR_DRIVER_VERSION,
                                       OID_GEN_XMIT_OK,
                                       OID_GEN_RCV_OK,
                                       OID_GEN_CURRENT_PACKET_FILTER,
                                       OID_GEN_STATISTICS,
                                       OID_GEN_INTERRUPT_MODERATION,
                                       OID_GEN_LINK_PARAMETERS,
                                       OID_PNP_SET_POWER,
                                       OID_PNP_QUERY_POWER};
    NDIS_MINIPORT_ADAPTER_GENERAL_ATTRIBUTES AdapterGeneralAttributes = {
        .Header = {.Type = NDIS_OBJECT_TYPE_MINIPORT_ADAPTER_GENERAL_ATTRIBUTES,
                   .Revision = NDIS_MINIPORT_ADAPTER_GENERAL_ATTRIBUTES_REVISION_1,
                   .Size = NDIS_SIZEOF_MINIPORT_ADAPTER_GENERAL_ATTRIBUTES_REVISION_1},
        .MediaType = NdisMediumIP,
        .PhysicalMediumType = NdisPhysicalMediumUnspecified,
        .MtuSize = VELOCITUN_MAX_IP_PACKET_SIZE,
        .MaxXmitLinkSpeed = VELOCITUN_LINK_SPEED,
        .MaxRcvLinkSpeed = VELOCITUN_LINK_SPEED,
        .RcvLinkSpeed = VELOCITUN_LINK_SPEED,
        .XmitLinkSpeed = VELOCITUN_LINK_SPEED,
        .MediaConnectState = MediaConnectStateDisconnected,
        .MediaDuplexState = MediaDuplexStateFull,
        .LookaheadSize = VELOCITUN_MAX_IP_PACKET_SIZE,
        .MacOptions =
            NDIS_MAC_OPTION_TRANSFERS_NOT_PEND | NDIS_MAC_OPTION_COPY_LOOKAHEAD_DATA | NDIS_MAC_OPTION_NO_LOOPBACK,
        .SupportedPacketFilters = NDIS_PACKET_TYPE_DIRECTED | NDIS_PACKET_TYPE_ALL_MULTICAST |
                                  NDIS_PACKET_TYPE_BROADCAST | NDIS_PACKET_TYPE_ALL_LOCAL |
                                  NDIS_PACKET_TYPE_ALL_FUNCTIONAL,
        .AccessType = NET_IF_ACCESS_BROADCAST,
        .DirectionType = NET_IF_DIRECTION_SENDRECEIVE,
        .ConnectionType = NET_IF_CONNECTION_DEDICATED,
        .IfType = IF_TYPE_PROP_VIRTUAL,
        .IfConnectorPresent = FALSE,
        .SupportedStatistics = Ctx->Statistics.SupportedStatistics,
        .SupportedPauseFunctions = NdisPauseFunctionsUnsupported,
        .AutoNegotiationFlags =
            NDIS_LINK_STATE_XMIT_LINK_SPEED_AUTO_NEGOTIATED | NDIS_LINK_STATE_RCV_LINK_SPEED_AUTO_NEGOTIATED |
            NDIS_LINK_STATE_DUPLEX_AUTO_NEGOTIATED | NDIS_LINK_STATE_PAUSE_FUNCTIONS_AUTO_NEGOTIATED,
        .SupportedOidList = SupportedOids,
        .SupportedOidListLength = sizeof(SupportedOids)};
    if (Status = NDIS_STATUS_FAILURE,
        !NT_SUCCESS(NdisMSetMiniportAttributes(
            MiniportAdapterHandle, (PNDIS_MINIPORT_ADAPTER_ATTRIBUTES)&AdapterGeneralAttributes)))
        goto cleanupFreeNblPool;

    return NDIS_STATUS_SUCCESS;

cleanupFreeNblPool:
    NdisFreeNetBufferListPool(Ctx->NblPool);
cleanupFreeCtx:
    ExFreePoolWithTag(Ctx, VELOCITUN_MEMORY_TAG);
    return Status;
}

static MINIPORT_HALT VelociTunHaltEx;
_Use_decl_annotations_ static VOID
VelociTunHaltEx(NDIS_HANDLE MiniportAdapterContext, NDIS_HALT_ACTION HaltAction)
{
    VELOCITUN_CTX *Ctx = (VELOCITUN_CTX *)MiniportAdapterContext;

    VelociTunUnregisterBuffers(Ctx, VELOCITUN_FORCE_UNREGISTRATION);

    ExReleaseSpinLockExclusive(
        &Ctx->TransitionLock,
        ExAcquireSpinLockExclusive(&Ctx->TransitionLock)); /* Ensure above change is visible to all readers. */
    NdisFreeNetBufferListPool(Ctx->NblPool);

#pragma warning(suppress : 6387)
    WritePointerNoFence(&Ctx->MiniportAdapterHandle, NULL);
#pragma warning(suppress : 6387 28175)
    WritePointerNoFence(&Ctx->FunctionalDeviceObject->Reserved, NULL);
    KeEnterCriticalRegion();
    ExAcquireResourceExclusiveLite(&VelociTunDispatchCtxGuard, TRUE); /* Ensure above change is visible to all readers. */
    ExReleaseResourceLite(&VelociTunDispatchCtxGuard);
    KeLeaveCriticalRegion();
    ExDeleteResourceLite(&Ctx->Device.RegistrationLock);
    ExFreePoolWithTag(Ctx, VELOCITUN_MEMORY_TAG);
}

static MINIPORT_SHUTDOWN VelociTunShutdownEx;
_Use_decl_annotations_ static VOID
VelociTunShutdownEx(NDIS_HANDLE MiniportAdapterContext, NDIS_SHUTDOWN_ACTION ShutdownAction)
{
}

_IRQL_requires_max_(APC_LEVEL)
    _Must_inspect_result_
    static NDIS_STATUS
    VelociTunOidQueryWrite(_Inout_ NDIS_OID_REQUEST *OidRequest, _In_ ULONG Value)
{
    if (OidRequest->DATA.QUERY_INFORMATION.InformationBufferLength < sizeof(ULONG))
    {
        OidRequest->DATA.QUERY_INFORMATION.BytesNeeded = sizeof(ULONG);
        OidRequest->DATA.QUERY_INFORMATION.BytesWritten = 0;
        return NDIS_STATUS_BUFFER_TOO_SHORT;
    }

    OidRequest->DATA.QUERY_INFORMATION.BytesNeeded = OidRequest->DATA.QUERY_INFORMATION.BytesWritten = sizeof(ULONG);
    *(ULONG *)OidRequest->DATA.QUERY_INFORMATION.InformationBuffer = Value;
    return NDIS_STATUS_SUCCESS;
}

_IRQL_requires_max_(APC_LEVEL)
    _Must_inspect_result_
    static NDIS_STATUS
    VelociTunOidQueryWrite32or64(_Inout_ NDIS_OID_REQUEST *OidRequest, _In_ ULONG64 Value)
{
    if (OidRequest->DATA.QUERY_INFORMATION.InformationBufferLength < sizeof(ULONG))
    {
        OidRequest->DATA.QUERY_INFORMATION.BytesNeeded = sizeof(ULONG64);
        OidRequest->DATA.QUERY_INFORMATION.BytesWritten = 0;
        return NDIS_STATUS_BUFFER_TOO_SHORT;
    }

    if (OidRequest->DATA.QUERY_INFORMATION.InformationBufferLength < sizeof(ULONG64))
    {
        OidRequest->DATA.QUERY_INFORMATION.BytesNeeded = OidRequest->DATA.QUERY_INFORMATION.BytesWritten = sizeof(ULONG);
        *(ULONG *)OidRequest->DATA.QUERY_INFORMATION.InformationBuffer = (ULONG)Value;
    }
    else
    {
        OidRequest->DATA.QUERY_INFORMATION.BytesNeeded = OidRequest->DATA.QUERY_INFORMATION.BytesWritten = sizeof(ULONG64);
        *(ULONG64 *)OidRequest->DATA.QUERY_INFORMATION.InformationBuffer = Value;
    }
    return NDIS_STATUS_SUCCESS;
}

_IRQL_requires_max_(APC_LEVEL)
    _Must_inspect_result_
    static NDIS_STATUS
    VelociTunOidQueryWriteBuf(_Inout_ NDIS_OID_REQUEST *OidRequest, _In_reads_bytes_(Size) const void *Buf, _In_ ULONG Size)
{
    if (OidRequest->DATA.QUERY_INFORMATION.InformationBufferLength < Size)
    {
        OidRequest->DATA.QUERY_INFORMATION.BytesNeeded = Size;
        OidRequest->DATA.QUERY_INFORMATION.BytesWritten = 0;
        return NDIS_STATUS_BUFFER_TOO_SHORT;
    }

    OidRequest->DATA.QUERY_INFORMATION.BytesNeeded = OidRequest->DATA.QUERY_INFORMATION.BytesWritten = Size;
    NdisMoveMemory(OidRequest->DATA.QUERY_INFORMATION.InformationBuffer, Buf, Size);
    return NDIS_STATUS_SUCCESS;
}

static MINIPORT_OID_REQUEST VelociTunOidRequest;
_Use_decl_annotations_ static NDIS_STATUS
VelociTunOidRequest(NDIS_HANDLE MiniportAdapterContext, PNDIS_OID_REQUEST OidRequest)
{
    VELOCITUN_CTX *Ctx = (VELOCITUN_CTX *)MiniportAdapterContext;

    switch (OidRequest->RequestType)
    {
    case NdisRequestQueryInformation:
    case NdisRequestQueryStatistics:
        switch (OidRequest->DATA.QUERY_INFORMATION.Oid)
        {
        case OID_GEN_MAXIMUM_TOTAL_SIZE:
        case OID_GEN_TRANSMIT_BLOCK_SIZE:
        case OID_GEN_RECEIVE_BLOCK_SIZE:
            return VelociTunOidQueryWrite(OidRequest, VELOCITUN_MAX_IP_PACKET_SIZE);

        case OID_GEN_TRANSMIT_BUFFER_SPACE:
        case OID_GEN_RECEIVE_BUFFER_SPACE:
            return VelociTunOidQueryWrite(OidRequest, VELOCITUN_MAX_RING_CAPACITY);

        case OID_GEN_VENDOR_ID:
            return VelociTunOidQueryWrite(OidRequest, VELOCITUN_VENDOR_ID);

        case OID_GEN_VENDOR_DESCRIPTION:
            return VelociTunOidQueryWriteBuf(OidRequest, VELOCITUN_VENDOR_NAME, sizeof(VELOCITUN_VENDOR_NAME));

        case OID_GEN_VENDOR_DRIVER_VERSION:
            return VelociTunOidQueryWrite(OidRequest, (0 << 16) | 14); // Version 0.14

        case OID_GEN_XMIT_OK:
            return VelociTunOidQueryWrite32or64(OidRequest, Ctx->Statistics.ifHCOutUcastPkts);

        case OID_GEN_RCV_OK:
            return VelociTunOidQueryWrite32or64(OidRequest, Ctx->Statistics.ifHCInUcastPkts);

        case OID_GEN_STATISTICS:
            return VelociTunOidQueryWriteBuf(OidRequest, &Ctx->Statistics, sizeof(Ctx->Statistics));

        case OID_GEN_INTERRUPT_MODERATION:
        {
            static const NDIS_INTERRUPT_MODERATION_PARAMETERS InterruptModeration = {
                .Header = {.Type = NDIS_OBJECT_TYPE_DEFAULT,
                           .Revision = NDIS_INTERRUPT_MODERATION_PARAMETERS_REVISION_1,
                           .Size = NDIS_SIZEOF_INTERRUPT_MODERATION_PARAMETERS_REVISION_1},
                .InterruptModeration = NdisInterruptModerationNotSupported};
            return VelociTunOidQueryWriteBuf(OidRequest, &InterruptModeration, sizeof(InterruptModeration));
        }

        case OID_PNP_QUERY_POWER:
            return NDIS_STATUS_SUCCESS;

        case OID_GEN_CURRENT_PACKET_FILTER:
        case OID_GEN_CURRENT_LOOKAHEAD:
        case OID_GEN_LINK_PARAMETERS:
            return NDIS_STATUS_NOT_SUPPORTED;
        }
        break;

    case NdisRequestSetInformation:
        switch (OidRequest->DATA.SET_INFORMATION.Oid)
        {
        case OID_GEN_CURRENT_PACKET_FILTER:
        case OID_GEN_CURRENT_LOOKAHEAD:
        case OID_GEN_LINK_PARAMETERS:
        case OID_PNP_SET_POWER:
            return NDIS_STATUS_SUCCESS;
        }
        break;

    case NdisRequestMethod:
        break;
    }

    return NDIS_STATUS_INVALID_OID;
}

static MINIPORT_CANCEL_OID_REQUEST VelociTunCancelOidRequest;
_Use_decl_annotations_ static VOID
VelociTunCancelOidRequest(NDIS_HANDLE MiniportAdapterContext, PVOID RequestId)
{
}

NTSTATUS
DriverEntry(_In_ PDRIVER_OBJECT DriverObject, _In_ PUNICODE_STRING RegistryPath)
{
    NDIS_STATUS NdisStatus;
    NDIS_MINIPORT_DRIVER_CHARACTERISTICS MiniportDriverCharacteristics = {
        .Header = {.Type = NDIS_OBJECT_TYPE_MINIPORT_DRIVER_CHARACTERISTICS,
                   .Revision = NDIS_MINIPORT_DRIVER_CHARACTERISTICS_REVISION_1,
                   .Size = NDIS_SIZEOF_MINIPORT_DRIVER_CHARACTERISTICS_REVISION_1},
        .MajorNdisVersion = NDIS_MINIPORT_MAJOR_VERSION,
        .MinorNdisVersion = NDIS_MINIPORT_MINOR_VERSION,
        .MajorDriverVersion = 0,
        .MinorDriverVersion = 14,
        .InitializeHandlerEx = VelociTunInitializeEx,
        .HaltHandlerEx = VelociTunHaltEx,
        .UnloadHandler = NULL,
        .PauseHandler = VelociTunPause,
        .RestartHandler = VelociTunRestart,
        .OidRequestHandler = VelociTunOidRequest,
        .SendNetBufferListsHandler = VelociTunSendNetBufferLists,
        .ReturnNetBufferListsHandler = VelociTunReturnNetBufferLists,
        .CancelSendHandler = VelociTunCancelSend,
        .DevicePnPEventNotifyHandler = VelociTunDevicePnPEventNotify,
        .ShutdownHandlerEx = VelociTunShutdownEx,
        .CancelOidRequestHandler = VelociTunCancelOidRequest};

    NdisVersion = NdisGetVersion();
    if (NdisVersion < NDIS_MINIPORT_VERSION_MIN)
        return NDIS_STATUS_UNSUPPORTED_REVISION;
    if (NdisVersion > NDIS_MINIPORT_VERSION_MAX)
        NdisVersion = NDIS_MINIPORT_VERSION_MAX;

    NdisStatus = NdisMRegisterMiniportDriver(DriverObject, RegistryPath, NULL, &MiniportDriverCharacteristics, &NdisMiniportDriverHandle);
    if (!NT_SUCCESS(NdisStatus))
        return NdisStatus;

    NdisDispatchDeviceControl = DriverObject->MajorFunction[IRP_MJ_DEVICE_CONTROL];
    NdisDispatchClose = DriverObject->MajorFunction[IRP_MJ_CLOSE];
    NdisDispatchPnp = DriverObject->MajorFunction[IRP_MJ_PNP];
    DriverObject->MajorFunction[IRP_MJ_DEVICE_CONTROL] = VelociTunDispatchDeviceControl;
    DriverObject->MajorFunction[IRP_MJ_CLOSE] = VelociTunDispatchClose;
    DriverObject->MajorFunction[IRP_MJ_PNP] = VelociTunDispatchPnp;

    ExInitializeResourceLite(&VelociTunDispatchCtxGuard);
    ExInitializeResourceLite(&VelociTunDispatchDeviceListLock);

    NTSTATUS Status = PsSetCreateProcessNotifyRoutine(VelociTunProcessNotification, FALSE);
    if (!NT_SUCCESS(Status))
    {
        ExDeleteResourceLite(&VelociTunDispatchDeviceListLock);
        ExDeleteResourceLite(&VelociTunDispatchCtxGuard);
        NdisMDeregisterMiniportDriver(NdisMiniportDriverHandle);
        return Status;
    }

    return NDIS_STATUS_SUCCESS;
}

VOID DriverUnload(_In_ PDRIVER_OBJECT DriverObject)
{
    PsSetCreateProcessNotifyRoutine(VelociTunProcessNotification, TRUE);
    ExDeleteResourceLite(&VelociTunDispatchDeviceListLock);
    ExDeleteResourceLite(&VelociTunDispatchCtxGuard);
    NdisMDeregisterMiniportDriver(NdisMiniportDriverHandle);
}
