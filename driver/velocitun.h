/* SPDX-License-Identifier: GPL-2.0
 *
 * Copyright (C) 2024 VelociTun Team. All Rights Reserved.
 * VelociTun driver header file.
 */

#pragma once

#include <ntifs.h>
#include <wdm.h>
#include <wdmsec.h>
#include <ndis.h>
#include <ntstrsafe.h>

#ifdef __cplusplus
extern "C" {
#endif

// VelociTun driver constants
#define VELOCITUN_VENDOR_NAME "VelociTun High-Speed Tunnel"
#define VELOCITUN_VENDOR_ID 0xFFFFFF01
#define VELOCITUN_LINK_SPEED 100000000000ULL /* 100gbps */

/* Memory alignment of packets and rings */
#define VELOCITUN_ALIGNMENT sizeof(ULONG)
#define VELOCITUN_ALIGN(Size) (((ULONG)(Size) + ((ULONG)VELOCITUN_ALIGNMENT - 1)) & ~((ULONG)VELOCITUN_ALIGNMENT - 1))
#define VELOCITUN_IS_ALIGNED(Size) (!((ULONG)(Size) & ((ULONG)VELOCITUN_ALIGNMENT - 1)))

/* Maximum IP packet size */
#define VELOCITUN_MAX_IP_PACKET_SIZE 0xFFFF
/* Maximum packet size */
#define VELOCITUN_MAX_PACKET_SIZE VELOCITUN_ALIGN(sizeof(VELOCITUN_PACKET) + VELOCITUN_MAX_IP_PACKET_SIZE)

/* Minimum ring capacity. */
#define VELOCITUN_MIN_RING_CAPACITY 0x20000 /* 128kiB */
/* Maximum ring capacity. */
#define VELOCITUN_MAX_RING_CAPACITY 0x4000000 /* 64MiB */

/* Calculates ring capacity */
#define VELOCITUN_RING_CAPACITY(Size) ((Size) - sizeof(VELOCITUN_RING) - (VELOCITUN_MAX_PACKET_SIZE - VELOCITUN_ALIGNMENT))
/* Calculates ring offset modulo capacity */
#define VELOCITUN_RING_WRAP(Value, Capacity) ((Value) & (Capacity - 1))

// Byte order conversion macros
#if REG_DWORD == REG_DWORD_BIG_ENDIAN
#    define HTONS(x) ((USHORT)(x))
#    define HTONL(x) ((ULONG)(x))
#elif REG_DWORD == REG_DWORD_LITTLE_ENDIAN
#    define HTONS(x) ((((USHORT)(x)&0x00ff) << 8) | (((USHORT)(x)&0xff00) >> 8))
#    define HTONL(x) \
        ((((ULONG)(x)&0x000000ff) << 24) | (((ULONG)(x)&0x0000ff00) << 8) | (((ULONG)(x)&0x00ff0000) >> 8) | \
         (((ULONG)(x)&0xff000000) >> 24))
#else
#    error "REG_DWORD must be defined"
#endif

// NDIS constants that might be missing in older headers
#ifndef NDIS_NBL_FLAGS_IS_IPV4
#define NDIS_NBL_FLAGS_IS_IPV4 0x00000001
#endif

#ifndef NDIS_NBL_FLAGS_IS_IPV6
#define NDIS_NBL_FLAGS_IS_IPV6 0x00000002
#endif

#ifndef NDIS_ETH_TYPE_IPV4
#define NDIS_ETH_TYPE_IPV4 0x0800
#endif

#ifndef NDIS_ETH_TYPE_IPV6
#define NDIS_ETH_TYPE_IPV6 0x86DD
#endif

// NDIS version constants
#ifndef NDIS_MINIPORT_VERSION_MIN
#define NDIS_MINIPORT_VERSION_MIN ((NDIS_MINIPORT_MINIMUM_MAJOR_VERSION << 16) | NDIS_MINIPORT_MINIMUM_MINOR_VERSION)
#endif

#ifndef NDIS_MINIPORT_VERSION_MAX
#define NDIS_MINIPORT_VERSION_MAX ((NDIS_MINIPORT_MAJOR_VERSION << 16) | NDIS_MINIPORT_MINOR_VERSION)
#endif

// Missing NDIS revision constants for older WDK versions
#ifndef NDIS_MINIPORT_ADAPTER_REGISTRATION_ATTRIBUTES_REVISION_2
#define NDIS_MINIPORT_ADAPTER_REGISTRATION_ATTRIBUTES_REVISION_2 2
#endif

#ifndef NDIS_SIZEOF_MINIPORT_ADAPTER_REGISTRATION_ATTRIBUTES_REVISION_2
#define NDIS_SIZEOF_MINIPORT_ADAPTER_REGISTRATION_ATTRIBUTES_REVISION_2 \
    RTL_SIZEOF_THROUGH_FIELD(NDIS_MINIPORT_ADAPTER_REGISTRATION_ATTRIBUTES, CheckForHangTimeInSeconds)
#endif

#ifndef NDIS_MINIPORT_ADAPTER_GENERAL_ATTRIBUTES_REVISION_2
#define NDIS_MINIPORT_ADAPTER_GENERAL_ATTRIBUTES_REVISION_2 2
#endif

#ifndef NDIS_SIZEOF_MINIPORT_ADAPTER_GENERAL_ATTRIBUTES_REVISION_2
#define NDIS_SIZEOF_MINIPORT_ADAPTER_GENERAL_ATTRIBUTES_REVISION_2 \
    RTL_SIZEOF_THROUGH_FIELD(NDIS_MINIPORT_ADAPTER_GENERAL_ATTRIBUTES, PowerManagementCapabilitiesEx)
#endif

#ifndef NDIS_MINIPORT_DRIVER_CHARACTERISTICS_REVISION_2
#define NDIS_MINIPORT_DRIVER_CHARACTERISTICS_REVISION_2 2
#endif

#ifndef NDIS_SIZEOF_MINIPORT_DRIVER_CHARACTERISTICS_REVISION_2
#define NDIS_SIZEOF_MINIPORT_DRIVER_CHARACTERISTICS_REVISION_2 \
    RTL_SIZEOF_THROUGH_FIELD(NDIS_MINIPORT_DRIVER_CHARACTERISTICS, DirectOidRequestCompleteHandler)
#endif

#define VELOCITUN_MEMORY_TAG HTONL('vtun')

// Forward declarations
typedef struct _VELOCITUN_PACKET VELOCITUN_PACKET;
typedef struct _VELOCITUN_RING VELOCITUN_RING;
typedef struct _VELOCITUN_REGISTER_RINGS VELOCITUN_REGISTER_RINGS;
typedef struct _VELOCITUN_CTX VELOCITUN_CTX;

// Packet structure
typedef struct _VELOCITUN_PACKET
{
    /* Size of packet data (VELOCITUN_MAX_IP_PACKET_SIZE max) */
    ULONG Size;

    /* Packet data */
    UCHAR _Field_size_bytes_(Size)
    Data[];
} VELOCITUN_PACKET;

// Ring buffer structure
typedef struct _VELOCITUN_RING
{
    /* Byte offset of the first packet in the ring. Its value must be a multiple of VELOCITUN_ALIGNMENT and less than ring
     * capacity. */
    volatile ULONG Head;

    /* Byte offset of the first free space in the ring. Its value must be multiple of VELOCITUN_ALIGNMENT and less than ring
     * capacity. */
    volatile ULONG Tail;

    /* Non-zero when consumer is in alertable state. */
    volatile LONG Alertable;

    /* Ring data. Its capacity must be a power of 2 + extra VELOCITUN_MAX_PACKET_SIZE-VELOCITUN_ALIGNMENT space to
     * eliminate need for wrapping. */
    UCHAR Data[];
} VELOCITUN_RING;

// Ring registration structure
typedef struct _VELOCITUN_REGISTER_RINGS
{
    struct
    {
        /* Size of the ring */
        ULONG RingSize;

        /* Pointer to client allocated ring */
        VELOCITUN_RING *Ring;

        /* On send: An event created by the client the VelociTun signals after it moves the Tail member of the send ring.
         * On receive: An event created by the client the client will signal when it moves the Tail member of
         * the receive ring if receive ring is alertable. */
        HANDLE TailMoved;
    } Send, Receive;
} VELOCITUN_REGISTER_RINGS;

#ifdef _WIN64
typedef struct _VELOCITUN_REGISTER_RINGS_32
{
    struct
    {
        /* Size of the ring */
        ULONG RingSize;

        /* 32-bit address of client allocated ring */
        ULONG Ring;

        /* On send: An event created by the client the VelociTun signals after it moves the Tail member of the send ring.
         * On receive: An event created by the client the client will signal when it moves the Tail member of
         * the receive ring if receive ring is alertable. */
        ULONG TailMoved;
    } Send, Receive;
} VELOCITUN_REGISTER_RINGS_32;
#endif

// IOCTL definitions
#define VELOCITUN_IOCTL_REGISTER_RINGS CTL_CODE(51821U, 0x970U, METHOD_BUFFERED, FILE_READ_DATA | FILE_WRITE_DATA)

// Main driver context structure
typedef struct _VELOCITUN_CTX
{
    volatile LONG Running;

    /* Used like RCU. When we're making use of rings, we take a shared lock. When we want to register or release the
     * rings and toggle the state, we take an exclusive lock before toggling the atomic and then releasing. It's similar
     * to setting the atomic and then calling rcu_barrier(). */
    EX_SPIN_LOCK TransitionLock;

    NDIS_HANDLE MiniportAdapterHandle; /* This is actually a pointer to NDIS_MINIPORT_BLOCK struct. */
    DEVICE_OBJECT *FunctionalDeviceObject;
    NDIS_STATISTICS_INFO Statistics;

    struct
    {
        LIST_ENTRY Entry;
        ERESOURCE RegistrationLock;
        FILE_OBJECT *OwningFileObject;
        HANDLE OwningProcessId;
        KEVENT Disconnected;

        struct
        {
            MDL *Mdl;
            VELOCITUN_RING *Ring;
            ULONG Capacity;
            KEVENT *TailMoved;
            KSPIN_LOCK Lock;
            ULONG RingTail;
            struct
            {
                NET_BUFFER_LIST *Head, *Tail;
            } ActiveNbls;
        } Send;

        struct
        {
            MDL *Mdl;
            VELOCITUN_RING *Ring;
            ULONG Capacity;
            KEVENT *TailMoved;
            HANDLE Thread;
            KSPIN_LOCK Lock;
            struct
            {
                NET_BUFFER_LIST *Head, *Tail;
                KEVENT Empty;
            } ActiveNbls;
        } Receive;
    } Device;

    NDIS_HANDLE NblPool;
} VELOCITUN_CTX;

// Function declarations
extern UINT NdisVersion;
extern NDIS_HANDLE NdisMiniportDriverHandle;
extern DRIVER_DISPATCH *NdisDispatchDeviceControl, *NdisDispatchClose, *NdisDispatchPnp;
extern ERESOURCE VelociTunDispatchCtxGuard, VelociTunDispatchDeviceListLock;

// Core driver functions
VOID VelociTunIndicateStatus(_In_ NDIS_HANDLE MiniportAdapterHandle, _In_ NDIS_MEDIA_CONNECT_STATE MediaConnectState);
VOID VelociTunSendNetBufferLists(NDIS_HANDLE MiniportAdapterContext, NET_BUFFER_LIST *NetBufferLists, NDIS_PORT_NUMBER PortNumber, ULONG SendFlags);
VOID VelociTunCancelSend(NDIS_HANDLE MiniportAdapterContext, PVOID CancelId);
VOID VelociTunReturnNetBufferLists(NDIS_HANDLE MiniportAdapterContext, PNET_BUFFER_LIST NetBufferLists, ULONG ReturnFlags);
VOID VelociTunProcessReceiveData(_Inout_ VELOCITUN_CTX *Ctx);

// Buffer management functions
NTSTATUS VelociTunRegisterBuffers(_Inout_ VELOCITUN_CTX *Ctx, _Inout_ IRP *Irp);
VOID VelociTunUnregisterBuffers(_Inout_ VELOCITUN_CTX *Ctx, _In_ FILE_OBJECT *Owner);
VOID VelociTunProcessNotification(HANDLE ParentId, HANDLE ProcessId, BOOLEAN Create);

// Dispatch functions
NTSTATUS VelociTunDispatchDeviceControl(DEVICE_OBJECT *DeviceObject, IRP *Irp);
NTSTATUS VelociTunDispatchClose(DEVICE_OBJECT *DeviceObject, IRP *Irp);
NTSTATUS VelociTunDispatchPnp(DEVICE_OBJECT *DeviceObject, IRP *Irp);

// NDIS miniport functions
NDIS_STATUS VelociTunRestart(NDIS_HANDLE MiniportAdapterContext, PNDIS_MINIPORT_RESTART_PARAMETERS MiniportRestartParameters);
NDIS_STATUS VelociTunPause(NDIS_HANDLE MiniportAdapterContext, PNDIS_MINIPORT_PAUSE_PARAMETERS MiniportPauseParameters);
VOID VelociTunDevicePnPEventNotify(NDIS_HANDLE MiniportAdapterContext, PNET_DEVICE_PNP_EVENT NetDevicePnPEvent);
NDIS_STATUS VelociTunInitializeEx(NDIS_HANDLE MiniportAdapterHandle, NDIS_HANDLE MiniportDriverContext, PNDIS_MINIPORT_INIT_PARAMETERS MiniportInitParameters);
VOID VelociTunHaltEx(NDIS_HANDLE MiniportAdapterContext, NDIS_HALT_ACTION HaltAction);
VOID VelociTunShutdownEx(NDIS_HANDLE MiniportAdapterContext, NDIS_SHUTDOWN_ACTION ShutdownAction);

// OID handling functions
NDIS_STATUS VelociTunOidRequest(NDIS_HANDLE MiniportAdapterContext, PNDIS_OID_REQUEST OidRequest);
VOID VelociTunCancelOidRequest(NDIS_HANDLE MiniportAdapterContext, PVOID RequestId);

// Utility functions
VOID VelociTunNblSetOffsetAndMarkActive(_Inout_ NET_BUFFER_LIST *Nbl, _In_ ULONG Offset);
ULONG VelociTunNblGetOffset(_In_ NET_BUFFER_LIST *Nbl);
VOID VelociTunNblMarkCompleted(_Inout_ NET_BUFFER_LIST *Nbl);
BOOLEAN VelociTunNblIsCompleted(_In_ NET_BUFFER_LIST *Nbl);

#ifdef __cplusplus
}
#endif
