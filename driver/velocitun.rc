/* SPDX-License-Identifier: GPL-2.0
 *
 * Copyright (C) 2024 VelociTun Team. All Rights Reserved.
 */

#include <windows.h>
#include <ntverp.h>

#define STRINGIZE(x) #x
#define EXPAND(x) STRINGIZE(x)

VS_VERSION_INFO VERSIONINFO
FILEVERSION    0,14,1,0
PRODUCTVERSION 0,14,1,0
FILEFLAGSMASK  VS_FFI_FILEFLAGSMASK
FILEFLAGS      0x0L
FILEOS         VOS_NT_WINDOWS32
FILETYPE       VFT_DRV
FILESUBTYPE    VFT2_DRV_NETWORK
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904b0"
        BEGIN
            VALUE "CompanyName", "VelociTun Team"
            VALUE "FileDescription", "VelociTun High-Speed Network Tunnel Driver"
            VALUE "FileVersion", "********"
            VALUE "InternalName", "velocitun.sys"
            VALUE "LegalCopyright", "Copyright (C) 2024 VelociTun Team. All Rights Reserved."
            VALUE "OriginalFilename", "velocitun.sys"
            VALUE "ProductName", "VelociTun"
            VALUE "ProductVersion", "********"
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x409, 1200
    END
END
