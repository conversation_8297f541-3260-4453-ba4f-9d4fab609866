use crate::{VelociTunError, VelociTunResult};

pub const MAX_IP_PACKET_SIZE: usize = 0xFFFF;
pub const ALIGNMENT: usize = std::mem::size_of::<u32>();
pub const MAX_PACKET_SIZE: usize =
    align_up(std::mem::size_of::<u32>() + MAX_IP_PACKET_SIZE, ALIGNMENT);

/// Network packet representation
#[derive(Debug, Clone)]
pub struct Packet {
    data: Vec<u8>,
}

impl Packet {
    /// Create a new packet from data
    pub fn new(data: &[u8]) -> VelociTunResult<Self> {
        if data.len() > MAX_IP_PACKET_SIZE {
            return Err(VelociTunError::InvalidParameter(
                "Packet too large".to_string(),
            ));
        }

        Ok(Packet {
            data: data.to_vec(),
        })
    }

    /// Create packet from raw bytes (including size prefix)
    pub fn from_bytes(bytes: &[u8]) -> VelociTunResult<Self> {
        if bytes.len() < std::mem::size_of::<u32>() {
            return Err(VelociTunError::InvalidData);
        }

        let size = u32::from_le_bytes([bytes[0], bytes[1], bytes[2], bytes[3]]) as usize;

        if size > MAX_IP_PACKET_SIZE {
            return Err(VelociTunError::InvalidData);
        }

        if bytes.len() < std::mem::size_of::<u32>() + size {
            return Err(VelociTunError::InvalidData);
        }

        let data = bytes[std::mem::size_of::<u32>()..std::mem::size_of::<u32>() + size].to_vec();

        Ok(Packet { data })
    }

    /// Get packet data
    pub fn data(&self) -> &[u8] {
        &self.data
    }

    /// Get mutable packet data
    pub fn data_mut(&mut self) -> &mut [u8] {
        &mut self.data
    }

    /// Get packet size
    pub fn size(&self) -> usize {
        self.data.len()
    }

    /// Get total size including header
    pub fn total_size(&self) -> usize {
        align_up(std::mem::size_of::<u32>() + self.data.len(), ALIGNMENT)
    }

    /// Convert packet to bytes (including size prefix)
    pub fn as_bytes(&self) -> Vec<u8> {
        let total_size = self.total_size();
        let mut bytes = vec![0u8; total_size];

        // Write size prefix
        let size_bytes = (self.data.len() as u32).to_le_bytes();
        bytes[0..4].copy_from_slice(&size_bytes);

        // Write data
        bytes[4..4 + self.data.len()].copy_from_slice(&self.data);

        bytes
    }

    /// Check if packet is valid IP packet
    pub fn is_valid_ip(&self) -> bool {
        if self.data.len() < 20 {
            return false; // Too small for IP header
        }

        let version = (self.data[0] >> 4) & 0xF;
        version == 4 || version == 6
    }

    /// Get IP version (4 or 6)
    pub fn ip_version(&self) -> Option<u8> {
        if self.data.is_empty() {
            return None;
        }

        let version = (self.data[0] >> 4) & 0xF;
        if version == 4 || version == 6 {
            Some(version)
        } else {
            None
        }
    }

    /// Get source IP address (for IPv4)
    pub fn source_ipv4(&self) -> Option<[u8; 4]> {
        if self.ip_version() != Some(4) || self.data.len() < 20 {
            return None;
        }

        Some([self.data[12], self.data[13], self.data[14], self.data[15]])
    }

    /// Get destination IP address (for IPv4)
    pub fn destination_ipv4(&self) -> Option<[u8; 4]> {
        if self.ip_version() != Some(4) || self.data.len() < 20 {
            return None;
        }

        Some([self.data[16], self.data[17], self.data[18], self.data[19]])
    }

    /// Get protocol (for IPv4)
    pub fn protocol(&self) -> Option<u8> {
        if self.ip_version() != Some(4) || self.data.len() < 20 {
            return None;
        }

        Some(self.data[9])
    }

    /// Create an empty packet
    pub fn empty() -> Self {
        Packet { data: Vec::new() }
    }

    /// Resize packet data
    pub fn resize(&mut self, new_size: usize) -> VelociTunResult<()> {
        if new_size > MAX_IP_PACKET_SIZE {
            return Err(VelociTunError::InvalidParameter(
                "Packet too large".to_string(),
            ));
        }

        self.data.resize(new_size, 0);
        Ok(())
    }

    /// Truncate packet data
    pub fn truncate(&mut self, new_size: usize) {
        if new_size <= self.data.len() {
            self.data.truncate(new_size);
        }
    }

    /// Append data to packet
    pub fn append(&mut self, data: &[u8]) -> VelociTunResult<()> {
        if self.data.len() + data.len() > MAX_IP_PACKET_SIZE {
            return Err(VelociTunError::InvalidParameter(
                "Packet would be too large".to_string(),
            ));
        }

        self.data.extend_from_slice(data);
        Ok(())
    }
}

impl std::fmt::Display for Packet {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "Packet({} bytes", self.data.len())?;

        if let Some(version) = self.ip_version() {
            write!(f, ", IPv{}", version)?;

            if version == 4 {
                if let (Some(src), Some(dst)) = (self.source_ipv4(), self.destination_ipv4()) {
                    write!(
                        f,
                        ", {}.{}.{}.{} -> {}.{}.{}.{}",
                        src[0], src[1], src[2], src[3], dst[0], dst[1], dst[2], dst[3]
                    )?;
                }

                if let Some(proto) = self.protocol() {
                    let proto_name = match proto {
                        1 => "ICMP",
                        6 => "TCP",
                        17 => "UDP",
                        _ => "Unknown",
                    };
                    write!(f, ", {}", proto_name)?;
                }
            }
        }

        write!(f, ")")
    }
}

impl AsRef<[u8]> for Packet {
    fn as_ref(&self) -> &[u8] {
        &self.data
    }
}

impl AsMut<[u8]> for Packet {
    fn as_mut(&mut self) -> &mut [u8] {
        &mut self.data
    }
}

impl From<Vec<u8>> for Packet {
    fn from(data: Vec<u8>) -> Self {
        Packet { data }
    }
}

impl From<&[u8]> for Packet {
    fn from(data: &[u8]) -> Self {
        Packet {
            data: data.to_vec(),
        }
    }
}

/// Align size up to the next multiple of alignment
pub const fn align_up(size: usize, alignment: usize) -> usize {
    (size + alignment - 1) & !(alignment - 1)
}

/// Check if size is aligned
pub const fn is_aligned(size: usize, alignment: usize) -> bool {
    size & (alignment - 1) == 0
}
