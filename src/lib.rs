pub mod adapter;
pub mod driver;
pub mod error;
pub mod logger;
pub mod packet;
pub mod registry;
pub mod resources;
pub mod ring;
pub mod session;

#[cfg(feature = "async")]
pub mod async_session;

pub use adapter::{Adapter, AdapterBuilder};
pub use driver::{Driver, DriverInfo, DriverVersion};
pub use error::{VelociTunError, VelociTunResult};
pub use packet::Packet;
pub use session::{Session, SessionStats};

#[cfg(feature = "async")]
pub use async_session::{
    AsyncPacketProcessor, AsyncPacketReceiver, AsyncPacketSender, AsyncSession, PacketStream,
};

use std::sync::Once;

static INIT: Once = Once::new();

/// Initialize the VelociTun library
pub fn init() {
    INIT.call_once(|| {
        logger::init_logger();
    });
}

/// Get library version
pub fn version() -> &'static str {
    env!("CARGO_PKG_VERSION")
}

/// Check if the library is running on Windows
pub fn is_windows() -> bool {
    cfg!(target_os = "windows")
}

/// Ensure the VelociTun driver is available
pub fn ensure_driver() -> VelociTunResult<()> {
    Driver::ensure_available()
}

/// Create a new adapter with default settings
pub fn create_adapter(name: &str) -> VelociTunResult<Adapter> {
    AdapterBuilder::new().name(name).build()
}

/// Find an existing adapter by name
pub fn find_adapter(name: &str) -> VelociTunResult<Adapter> {
    Adapter::find_by_name(name)
}

/// List all available VelociTun adapters
pub fn list_adapters() -> VelociTunResult<Vec<Adapter>> {
    Adapter::list_all()
}
