use crate::{
    adapter::Adapter,
    error::{Veloci<PERSON>unError, VelociTunResult},
    logger::*,
    session::{Session, SessionStats},
};
use std::{sync::Arc, time::Duration};
use tokio::{
    sync::{mpsc, Mutex},
    task::<PERSON><PERSON><PERSON><PERSON><PERSON>,
    time::timeout,
};

/// Async wrapper around the synchronous Session
pub struct AsyncSession {
    session: Arc<Session>,

    // Async channels for packet I/O
    packet_receiver: Arc<Mutex<mpsc::UnboundedReceiver<Vec<u8>>>>,

    // Background tasks
    receive_task: Option<JoinHandle<()>>,

    // Shutdown signal
    shutdown_sender: mpsc::UnboundedSender<()>,
}

impl AsyncSession {
    /// Create a new async session
    pub async fn new(adapter: Arc<Adapter>, capacity: u32) -> VelociTunResult<Self> {
        log_info!(
            "Creating async VelociTun session for adapter '{}' with capacity {}",
            adapter.name(),
            capacity
        );

        // Create the underlying synchronous session
        let session = Arc::new(Session::new(adapter, capacity)?);

        // Create channels for packet I/O
        let (packet_tx, packet_rx) = mpsc::unbounded_channel();
        let (shutdown_tx, mut shutdown_rx) = mpsc::unbounded_channel();

        // Create background receive task
        let session_clone = session.clone();
        let packet_tx_clone = packet_tx.clone();
        let mut shutdown_rx_clone = shutdown_rx.clone();

        let receive_task = tokio::spawn(async move {
            log_info!("Starting async receive task");

            loop {
                tokio::select! {
                    // Check for shutdown signal
                    _ = shutdown_rx_clone.recv() => {
                        log_info!("Receive task shutting down");
                        break;
                    }

                    // Try to receive packets
                    result = Self::receive_packet_async(&session_clone) => {
                        match result {
                            Ok(packet) => {
                                if let Err(_) = packet_tx_clone.send(packet) {
                                    log_warn!("Failed to send packet to channel - receiver dropped");
                                    break;
                                }
                            }
                            Err(VelociTunError::HandleEof) => {
                                log_info!("Session ended - receive task stopping");
                                break;
                            }
                            Err(e) => {
                                log_warn!("Receive error: {:?}", e);
                                // Continue on non-fatal errors
                            }
                        }
                    }
                }
            }

            log_info!("Async receive task completed");
        });

        Ok(AsyncSession {
            session,
            packet_receiver: Arc::new(Mutex::new(packet_rx)),
            receive_task: Some(receive_task),
            shutdown_sender: shutdown_tx,
        })
    }

    /// Send a packet asynchronously
    pub async fn send_packet(&self, data: &[u8]) -> VelociTunResult<()> {
        if !self.session.is_active() {
            return Err(VelociTunError::HandleEof);
        }

        // Use tokio::task::spawn_blocking for CPU-intensive work
        let session = self.session.clone();
        let data = data.to_vec();

        tokio::task::spawn_blocking(move || session.send_packet(&data))
            .await
            .map_err(|e| VelociTunError::InvalidParameter(format!("Task join error: {}", e)))?
    }

    /// Receive a packet asynchronously
    pub async fn receive_packet(&self) -> VelociTunResult<Vec<u8>> {
        let mut receiver = self.packet_receiver.lock().await;

        match receiver.recv().await {
            Some(packet) => {
                log_info!("Received async packet of {} bytes", packet.len());
                Ok(packet)
            }
            None => {
                log_info!("Packet receiver channel closed");
                Err(VelociTunError::HandleEof)
            }
        }
    }

    /// Receive a packet with timeout asynchronously
    pub async fn receive_packet_timeout(
        &self,
        timeout_duration: Duration,
    ) -> VelociTunResult<Vec<u8>> {
        match timeout(timeout_duration, self.receive_packet()).await {
            Ok(result) => result,
            Err(_) => Err(VelociTunError::WindowsApi(
                windows::core::Error::from_win32(),
            )),
        }
    }

    /// Try to receive a packet without blocking
    pub async fn try_receive_packet(&self) -> VelociTunResult<Option<Vec<u8>>> {
        let mut receiver = self.packet_receiver.lock().await;

        match receiver.try_recv() {
            Ok(packet) => {
                log_info!(
                    "Received async packet of {} bytes (non-blocking)",
                    packet.len()
                );
                Ok(Some(packet))
            }
            Err(mpsc::error::TryRecvError::Empty) => Ok(None),
            Err(mpsc::error::TryRecvError::Disconnected) => {
                log_info!("Packet receiver channel disconnected");
                Err(VelociTunError::HandleEof)
            }
        }
    }

    /// Get the underlying adapter
    pub fn adapter(&self) -> &Adapter {
        self.session.adapter()
    }

    /// Get the ring buffer capacity
    pub fn capacity(&self) -> u32 {
        self.session.capacity()
    }

    /// Check if the session is active
    pub fn is_active(&self) -> bool {
        self.session.is_active()
    }

    /// Get session statistics
    pub fn stats(&self) -> SessionStats {
        self.session.stats()
    }

    /// Shutdown the async session
    pub async fn shutdown(&mut self) {
        log_info!(
            "Shutting down async VelociTun session for adapter '{}'",
            self.session.adapter().name()
        );

        // Send shutdown signal
        let _ = self.shutdown_sender.send(());

        // Wait for background tasks to complete
        if let Some(receive_task) = self.receive_task.take() {
            let _ = receive_task.await;
        }

        // Shutdown the underlying session
        self.session.shutdown();
    }

    /// Internal async receive helper
    async fn receive_packet_async(session: &Arc<Session>) -> VelociTunResult<Vec<u8>> {
        // Use spawn_blocking to avoid blocking the async runtime
        let session = session.clone();

        tokio::task::spawn_blocking(move || {
            // Try non-blocking first
            match session.try_receive_packet()? {
                Some(packet) => Ok(packet),
                None => {
                    // If no packet available, wait with timeout
                    session.receive_packet_timeout(Duration::from_millis(100))
                }
            }
        })
        .await
        .map_err(|e| VelociTunError::InvalidParameter(format!("Task join error: {}", e)))?
    }
}

impl Drop for AsyncSession {
    fn drop(&mut self) {
        // Note: We can't use async in Drop, so we just send shutdown signal
        let _ = self.shutdown_sender.send(());
        log_info!("AsyncSession dropped");
    }
}

// Safety: AsyncSession is designed to be thread-safe
unsafe impl Send for AsyncSession {}
unsafe impl Sync for AsyncSession {}

/// Async packet stream for receiving packets
pub struct PacketStream {
    session: Arc<AsyncSession>,
}

impl PacketStream {
    /// Create a new packet stream
    pub fn new(session: Arc<AsyncSession>) -> Self {
        Self { session }
    }

    /// Get the next packet
    pub async fn next(&self) -> Option<VelociTunResult<Vec<u8>>> {
        if !self.session.is_active() {
            return None;
        }

        match self.session.receive_packet().await {
            Ok(packet) => Some(Ok(packet)),
            Err(VelociTunError::HandleEof) => None,
            Err(e) => Some(Err(e)),
        }
    }
}

/// Async packet sink for sending packets
pub struct PacketSink {
    session: Arc<AsyncSession>,
}

impl PacketSink {
    /// Create a new packet sink
    pub fn new(session: Arc<AsyncSession>) -> Self {
        Self { session }
    }

    /// Send a packet
    pub async fn send(&self, packet: Vec<u8>) -> VelociTunResult<()> {
        self.session.send_packet(&packet).await
    }

    /// Send multiple packets
    pub async fn send_all(&self, packets: Vec<Vec<u8>>) -> VelociTunResult<()> {
        for packet in packets {
            self.send(packet).await?;
        }
        Ok(())
    }
}
