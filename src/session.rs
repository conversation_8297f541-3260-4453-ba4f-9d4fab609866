use crate::{
    adapter::Adapter,
    error::{VelociTunError, VelociTunResult},
    logger::*,
};
use std::{
    mem, ptr,
    sync::{
        atomic::{AtomicBool, AtomicU32, Ordering},
        Arc,
    },
    time::{Duration, Instant},
};
use windows::{
    core::{<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>},
    Win32::{
        Foundation::{
            CloseH<PERSON>le, SetEvent, HANDLE, INVALID_HANDLE_VALUE, WAIT_OBJECT_0, WAIT_TIMEOUT,
        },
        System::{
            Memory::{
                VirtualAlloc, VirtualFree, MEM_COMMIT, MEM_RELEASE, MEM_RESERVE, PAGE_READWRITE,
            },
            Threading::{CreateEventW, WaitFor<PERSON>ingleObject},
            IO::DeviceIoControl,
        },
    },
};

// Constants from the VelociTun API
const TUN_ALIGNMENT: usize = 4; // sizeof(ULONG)
const TUN_MAX_IP_PACKET_SIZE: usize = 0xFFFF;
const TUN_PACKET_RELEASE: u32 = 0x80000000;

// IOCTL for registering rings with the driver
const TUN_IOCTL_REGISTER_RINGS: u32 = 0x22E970; // CTL_CODE(51820U, 0x970U, METHOD_BUFFERED, FILE_READ_DATA | FILE_WRITE_DATA)

// Align size to TUN_ALIGNMENT
fn tun_align(size: usize) -> usize {
    (size + TUN_ALIGNMENT - 1) & !(TUN_ALIGNMENT - 1)
}

// Maximum packet size including header
fn tun_max_packet_size() -> usize {
    tun_align(mem::size_of::<TunPacket>() + TUN_MAX_IP_PACKET_SIZE)
}

// Ring capacity calculation
fn tun_ring_capacity(size: usize) -> usize {
    size - mem::size_of::<TunRing>() - (tun_max_packet_size() - TUN_ALIGNMENT)
}

// Ring size calculation
fn tun_ring_size(capacity: usize) -> usize {
    mem::size_of::<TunRing>() + capacity + (tun_max_packet_size() - TUN_ALIGNMENT)
}

// Ring wrap calculation
fn tun_ring_wrap(value: u32, capacity: u32) -> u32 {
    value & (capacity - 1)
}

/// Packet structure in the ring buffer
#[repr(C)]
struct TunPacket {
    size: u32,
    // data follows immediately after
}

/// Ring buffer structure
#[repr(C)]
struct TunRing {
    head: AtomicU32,
    tail: AtomicU32,
    alertable: AtomicU32,
    // data follows immediately after
}

/// Ring registration structure for IOCTL
#[repr(C)]
struct TunRegisterRings {
    send: TunRingDescriptor,
    receive: TunRingDescriptor,
}

#[repr(C)]
struct TunRingDescriptor {
    ring_size: u32,
    ring: *mut TunRing,
    tail_moved: HANDLE,
}

/// High-performance session for VelociTun adapter I/O
pub struct Session {
    adapter: Arc<Adapter>,
    device_handle: HANDLE,
    capacity: u32,

    // Send ring (for receiving packets from the network stack)
    send_ring: *mut TunRing,
    send_tail_moved: HANDLE,
    send_head: AtomicU32,
    send_head_release: AtomicU32,
    send_packets_to_release: AtomicU32,

    // Receive ring (for sending packets to the network stack)
    receive_ring: *mut TunRing,
    receive_tail_moved: HANDLE,
    receive_tail: AtomicU32,
    receive_tail_release: AtomicU32,
    receive_packets_to_release: AtomicU32,

    // Memory region for both rings
    allocated_region: *mut u8,
    ring_size: usize,

    active: AtomicBool,
}

impl Session {
    /// Create a new VelociTun session
    pub fn new(adapter: Arc<Adapter>, capacity: u32) -> VelociTunResult<Self> {
        Self::validate_capacity(capacity)?;

        log_info!(
            "Creating VelociTun session for adapter '{}' with capacity {}",
            adapter.name(),
            capacity
        );

        // Calculate ring size
        let ring_size = tun_ring_size(capacity as usize);

        // Allocate memory for both rings (send + receive)
        let allocated_region = unsafe {
            VirtualAlloc(
                ptr::null_mut(),
                ring_size * 2,
                MEM_COMMIT | MEM_RESERVE,
                PAGE_READWRITE,
            )
        };

        if allocated_region.is_null() {
            return Err(VelociTunError::WindowsApi(Error::from_win32()));
        }

        // Setup ring pointers
        let send_ring = allocated_region as *mut TunRing;
        let receive_ring = unsafe { allocated_region.add(ring_size) as *mut TunRing };

        // Create events for ring notifications
        let send_tail_moved = unsafe { CreateEventW(ptr::null(), false, false, PCWSTR::null()) };
        let receive_tail_moved = unsafe { CreateEventW(ptr::null(), false, false, PCWSTR::null()) };

        if send_tail_moved.is_invalid() || receive_tail_moved.is_invalid() {
            unsafe {
                VirtualFree(allocated_region, 0, MEM_RELEASE);
                if !send_tail_moved.is_invalid() {
                    CloseHandle(send_tail_moved);
                }
                if !receive_tail_moved.is_invalid() {
                    CloseHandle(receive_tail_moved);
                }
            }
            return Err(VelociTunError::WindowsApi(Error::from_win32()));
        }

        // Open device handle to the adapter
        let device_handle = adapter.open_device_object()?;

        // Register rings with the driver
        let register_rings = TunRegisterRings {
            send: TunRingDescriptor {
                ring_size: ring_size as u32,
                ring: send_ring,
                tail_moved: send_tail_moved,
            },
            receive: TunRingDescriptor {
                ring_size: ring_size as u32,
                ring: receive_ring,
                tail_moved: receive_tail_moved,
            },
        };

        let mut bytes_returned = 0u32;
        let success = unsafe {
            DeviceIoControl(
                device_handle,
                TUN_IOCTL_REGISTER_RINGS,
                Some(&register_rings as *const _ as *const std::ffi::c_void),
                mem::size_of::<TunRegisterRings>() as u32,
                None,
                0,
                Some(&mut bytes_returned),
                None,
            )
        };

        if !success.as_bool() {
            unsafe {
                CloseHandle(device_handle);
                CloseHandle(send_tail_moved);
                CloseHandle(receive_tail_moved);
                VirtualFree(allocated_region, 0, MEM_RELEASE);
            }
            return Err(VelociTunError::WindowsApi(Error::from_win32()));
        }

        // Initialize ring structures
        unsafe {
            (*send_ring).head.store(0, Ordering::Relaxed);
            (*send_ring).tail.store(0, Ordering::Relaxed);
            (*send_ring).alertable.store(0, Ordering::Relaxed);

            (*receive_ring).head.store(0, Ordering::Relaxed);
            (*receive_ring).tail.store(0, Ordering::Relaxed);
            (*receive_ring).alertable.store(0, Ordering::Relaxed);
        }

        Ok(Session {
            adapter,
            device_handle,
            capacity,
            send_ring,
            send_tail_moved,
            send_head: AtomicU32::new(0),
            send_head_release: AtomicU32::new(0),
            send_packets_to_release: AtomicU32::new(0),
            receive_ring,
            receive_tail_moved,
            receive_tail: AtomicU32::new(0),
            receive_tail_release: AtomicU32::new(0),
            receive_packets_to_release: AtomicU32::new(0),
            allocated_region,
            ring_size,
            active: AtomicBool::new(true),
        })
    }

    /// Get the adapter associated with this session
    pub fn adapter(&self) -> &Adapter {
        &self.adapter
    }

    /// Get the ring buffer capacity
    pub fn capacity(&self) -> u32 {
        self.capacity
    }

    /// Check if the session is active
    pub fn is_active(&self) -> bool {
        self.active.load(Ordering::Acquire)
    }

    /// Send a packet to the network stack (thread-safe)
    pub fn send_packet(&self, data: &[u8]) -> VelociTunResult<()> {
        if !self.is_active() {
            return Err(VelociTunError::HandleEof);
        }

        if data.len() > TUN_MAX_IP_PACKET_SIZE {
            return Err(VelociTunError::InvalidParameter(
                "Packet too large".to_string(),
            ));
        }

        // Allocate send packet buffer
        let packet_buffer = self.allocate_send_packet(data.len() as u32)?;

        // Copy data to the buffer
        unsafe {
            ptr::copy_nonoverlapping(data.as_ptr(), packet_buffer, data.len());
        }

        // Send the packet
        self.send_packet_buffer(packet_buffer);

        log_info!("Sent packet of {} bytes to network stack", data.len());
        Ok(())
    }

    /// Allocate a send packet buffer (equivalent to WintunAllocateSendPacket)
    pub fn allocate_send_packet(&self, packet_size: u32) -> VelociTunResult<*mut u8> {
        if !self.is_active() {
            return Err(VelociTunError::HandleEof);
        }

        if packet_size > TUN_MAX_IP_PACKET_SIZE as u32 {
            return Err(VelociTunError::InvalidParameter(
                "Packet size too large".to_string(),
            ));
        }

        let aligned_packet_size =
            tun_align(mem::size_of::<TunPacket>() + packet_size as usize) as u32;

        unsafe {
            // Read current buffer head
            let buff_head = (*self.receive_ring).head.load(Ordering::Acquire);
            if buff_head >= self.capacity {
                return Err(VelociTunError::HandleEof);
            }

            // Calculate available space
            let current_tail = self.receive_tail.load(Ordering::Relaxed);
            let buff_space = tun_ring_wrap(
                buff_head
                    .wrapping_sub(current_tail)
                    .wrapping_sub(TUN_ALIGNMENT as u32),
                self.capacity,
            );

            if aligned_packet_size > buff_space {
                return Err(VelociTunError::BufferOverflow);
            }

            // Get packet buffer
            let ring_data = (self.receive_ring as *mut u8).add(mem::size_of::<TunRing>());
            let buff_packet = ring_data.add(current_tail as usize) as *mut TunPacket;

            // Set packet size with release flag
            (*buff_packet).size = packet_size | TUN_PACKET_RELEASE;

            // Update tail
            let new_tail = tun_ring_wrap(current_tail + aligned_packet_size, self.capacity);
            self.receive_tail.store(new_tail, Ordering::Relaxed);
            self.receive_packets_to_release
                .fetch_add(1, Ordering::Relaxed);

            // Return pointer to packet data
            Ok(buff_packet.add(1) as *mut u8)
        }
    }

    /// Send a packet buffer (equivalent to WintunSendPacket)
    pub fn send_packet_buffer(&self, packet: *mut u8) {
        unsafe {
            // Get packet header
            let buff_packet = (packet as *mut TunPacket).sub(1);

            // Clear release flag
            let size = (*buff_packet).size;
            (*buff_packet).size = size & !TUN_PACKET_RELEASE;

            // Process packets to release
            let mut packets_to_release = self.receive_packets_to_release.load(Ordering::Relaxed);
            while packets_to_release > 0 {
                let ring_data = (self.receive_ring as *mut u8).add(mem::size_of::<TunRing>());
                let current_release = self.receive_tail_release.load(Ordering::Relaxed);
                let buff_packet_release = ring_data.add(current_release as usize) as *mut TunPacket;

                if ((*buff_packet_release).size & TUN_PACKET_RELEASE) != 0 {
                    break;
                }

                let aligned_packet_size =
                    tun_align(mem::size_of::<TunPacket>() + ((*buff_packet_release).size as usize))
                        as u32;
                let new_tail_release =
                    tun_ring_wrap(current_release + aligned_packet_size, self.capacity);
                self.receive_tail_release
                    .store(new_tail_release, Ordering::Relaxed);
                packets_to_release -= 1;
            }
            self.receive_packets_to_release
                .store(packets_to_release, Ordering::Relaxed);

            // Update ring tail if needed
            let current_tail_release = self.receive_tail_release.load(Ordering::Relaxed);
            let ring_tail = (*self.receive_ring).tail.load(Ordering::Relaxed);
            if ring_tail != current_tail_release {
                (*self.receive_ring)
                    .tail
                    .store(current_tail_release, Ordering::Release);

                // Signal if alertable
                if (*self.receive_ring).alertable.load(Ordering::Acquire) != 0 {
                    let _ = SetEvent(self.receive_tail_moved);
                }
            }
        }
    }

    /// Receive a packet (thread-safe, blocking)
    pub fn receive_packet(&self) -> VelociTunResult<Vec<u8>> {
        self.receive_packet_timeout(Duration::from_secs(u64::MAX))
    }

    /// Receive a packet with timeout (thread-safe)
    pub fn receive_packet_timeout(&self, timeout: Duration) -> VelociTunResult<Vec<u8>> {
        if !self.is_active() {
            return Err(VelociTunError::HandleEof);
        }

        let start_time = Instant::now();

        loop {
            // Try to receive a packet from the send ring (packets from network stack)
            if let Some((packet_data, packet_ptr)) = self.try_receive_packet_internal()? {
                // Copy packet data
                let result = packet_data.to_vec();

                // Release the packet
                self.release_receive_packet(packet_ptr);

                log_info!(
                    "Received packet of {} bytes from network stack",
                    result.len()
                );
                return Ok(result);
            }

            // Check timeout
            if start_time.elapsed() >= timeout {
                return Err(VelociTunError::WindowsApi(Error::from_win32()));
            }

            // Wait for data with timeout
            let remaining_timeout = timeout.saturating_sub(start_time.elapsed());
            if remaining_timeout.is_zero() {
                return Err(VelociTunError::WindowsApi(Error::from_win32()));
            }

            let timeout_ms = remaining_timeout.as_millis().min(u32::MAX as u128) as u32;
            unsafe {
                let wait_result = WaitForSingleObject(self.send_tail_moved, timeout_ms);
                if wait_result == WAIT_TIMEOUT {
                    return Err(VelociTunError::WindowsApi(Error::from_win32()));
                }
            }
        }
    }

    /// Try to receive a packet without blocking (internal method)
    fn try_receive_packet_internal(&self) -> VelociTunResult<Option<(&[u8], *const u8)>> {
        unsafe {
            let current_head = self.send_head.load(Ordering::Relaxed);
            if current_head >= self.capacity {
                return Err(VelociTunError::HandleEof);
            }

            // Read ring tail
            let buff_tail = (*self.send_ring).tail.load(Ordering::Acquire);
            if buff_tail >= self.capacity {
                return Err(VelociTunError::HandleEof);
            }

            // Check if data is available
            if current_head == buff_tail {
                return Ok(None); // No data available
            }

            // Calculate buffer content
            let buff_content = tun_ring_wrap(buff_tail.wrapping_sub(current_head), self.capacity);
            if buff_content < mem::size_of::<TunPacket>() as u32 {
                return Err(VelociTunError::InvalidData);
            }

            // Get packet
            let ring_data = (self.send_ring as *mut u8).add(mem::size_of::<TunRing>());
            let buff_packet = ring_data.add(current_head as usize) as *mut TunPacket;
            let packet_size = (*buff_packet).size;

            if packet_size > TUN_MAX_IP_PACKET_SIZE as u32 {
                return Err(VelociTunError::InvalidData);
            }

            let aligned_packet_size =
                tun_align(mem::size_of::<TunPacket>() + packet_size as usize) as u32;
            if aligned_packet_size > buff_content {
                return Err(VelociTunError::InvalidData);
            }

            // Get packet data
            let packet_data_ptr = buff_packet.add(1) as *const u8;
            let packet_data = std::slice::from_raw_parts(packet_data_ptr, packet_size as usize);

            // Update head
            let new_head = tun_ring_wrap(current_head + aligned_packet_size, self.capacity);
            self.send_head.store(new_head, Ordering::Relaxed);
            self.send_packets_to_release.fetch_add(1, Ordering::Relaxed);

            Ok(Some((packet_data, packet_data_ptr)))
        }
    }

    /// Release a received packet (equivalent to WintunReleaseReceivePacket)
    pub fn release_receive_packet(&self, packet: *const u8) {
        unsafe {
            // Get packet header
            let buff_packet = (packet as *mut TunPacket).sub(1);

            // Mark packet as released
            let size = (*buff_packet).size;
            (*buff_packet).size = size | TUN_PACKET_RELEASE;

            // Process packets to release
            let mut packets_to_release = self.send_packets_to_release.load(Ordering::Relaxed);
            while packets_to_release > 0 {
                let ring_data = (self.send_ring as *mut u8).add(mem::size_of::<TunRing>());
                let current_release = self.send_head_release.load(Ordering::Relaxed);
                let buff_packet_release = ring_data.add(current_release as usize) as *mut TunPacket;

                if ((*buff_packet_release).size & TUN_PACKET_RELEASE) == 0 {
                    break;
                }

                let packet_size = (*buff_packet_release).size & !TUN_PACKET_RELEASE;
                let aligned_packet_size =
                    tun_align(mem::size_of::<TunPacket>() + packet_size as usize) as u32;
                let new_head_release =
                    tun_ring_wrap(current_release + aligned_packet_size, self.capacity);
                self.send_head_release
                    .store(new_head_release, Ordering::Relaxed);
                packets_to_release -= 1;
            }
            self.send_packets_to_release
                .store(packets_to_release, Ordering::Relaxed);

            // Update ring head
            let current_head_release = self.send_head_release.load(Ordering::Relaxed);
            (*self.send_ring)
                .head
                .store(current_head_release, Ordering::Release);
        }
    }

    /// Try to receive a packet without blocking (thread-safe)
    pub fn try_receive_packet(&self) -> VelociTunResult<Option<Vec<u8>>> {
        if !self.is_active() {
            return Err(VelociTunError::HandleEof);
        }

        if let Some((packet_data, packet_ptr)) = self.try_receive_packet_internal()? {
            let result = packet_data.to_vec();
            self.release_receive_packet(packet_ptr);
            log_info!(
                "Received packet of {} bytes from network stack",
                result.len()
            );
            Ok(Some(result))
        } else {
            Ok(None)
        }
    }

    /// Get statistics about the session
    pub fn stats(&self) -> SessionStats {
        unsafe {
            let send_head = (*self.send_ring).head.load(Ordering::Relaxed);
            let send_tail = (*self.send_ring).tail.load(Ordering::Relaxed);
            let receive_head = (*self.receive_ring).head.load(Ordering::Relaxed);
            let receive_tail = (*self.receive_ring).tail.load(Ordering::Relaxed);

            let send_used = tun_ring_wrap(send_tail.wrapping_sub(send_head), self.capacity);
            let send_available = self.capacity - send_used;
            let receive_used =
                tun_ring_wrap(receive_tail.wrapping_sub(receive_head), self.capacity);
            let receive_available = self.capacity - receive_used;

            SessionStats {
                send_ring_used: send_used,
                send_ring_available: send_available,
                receive_ring_used: receive_used,
                receive_ring_available: receive_available,
                capacity: self.capacity,
                is_active: self.is_active(),
            }
        }
    }

    /// Shutdown the session
    pub fn shutdown(&self) {
        log_info!(
            "Shutting down VelociTun session for adapter '{}'",
            self.adapter.name()
        );

        self.active.store(false, Ordering::Release);
    }

    fn validate_capacity(capacity: u32) -> VelociTunResult<()> {
        if !capacity.is_power_of_two() {
            return Err(VelociTunError::InvalidRingCapacity(capacity));
        }

        if !(crate::ring::MIN_RING_CAPACITY..=crate::ring::MAX_RING_CAPACITY).contains(&capacity) {
            return Err(VelociTunError::InvalidRingCapacity(capacity));
        }

        Ok(())
    }
}

impl Drop for Session {
    fn drop(&mut self) {
        self.shutdown();

        // Clean up resources
        unsafe {
            if !self.device_handle.is_invalid() {
                CloseHandle(self.device_handle);
            }
            if !self.send_tail_moved.is_invalid() {
                CloseHandle(self.send_tail_moved);
            }
            if !self.receive_tail_moved.is_invalid() {
                CloseHandle(self.receive_tail_moved);
            }
            if !self.allocated_region.is_null() {
                VirtualFree(self.allocated_region, 0, MEM_RELEASE);
            }
        }

        log_info!(
            "VelociTun session dropped for adapter '{}'",
            self.adapter.name()
        );
    }
}

/// Session statistics
#[derive(Debug, Clone)]
pub struct SessionStats {
    pub send_ring_used: u32,
    pub send_ring_available: u32,
    pub receive_ring_used: u32,
    pub receive_ring_available: u32,
    pub capacity: u32,
    pub is_active: bool,
}

// Safety: Session is designed to be thread-safe for its specific operations
unsafe impl Send for Session {}
unsafe impl Sync for Session {}
