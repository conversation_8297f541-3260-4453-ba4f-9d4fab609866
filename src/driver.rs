use std::{fmt, fs, mem, path, ptr, slice};

use windows::Win32::{
    Devices::DeviceAndDriverInstallation::{
        SetupDiBuildDriverInfoList, SetupDiCreateDeviceInfoListExW, SetupDiCreateDeviceInfoW,
        SetupDiDestroyDeviceInfoList, SetupDiDestroyDriverInfoList,
        SetupDiSetDeviceRegistryPropertyW, DICD_GENERATE_ID, GUID_DEVCLASS_NET, SPDIT_COMPATDRIVER,
        SPDRP_HARDWAREID, SP_DEVINFO_DATA,
    },
    Foundation::FALSE,
    Storage::FileSystem::{
        GetFileVersionInfoSizeW, GetFileVersionInfoW, VerQueryValueW, VS_FIXEDFILEINFO,
    },
};

use crate::{logger::*, VelociTunError, VelociTunResult};

/// VelociTun driver manager
#[derive(Debug)]
pub struct Driver;

impl Driver {
    /// Get the version of the running driver
    pub fn version() -> Option<DriverVersion> {
        match Self::get_running_driver_version() {
            Ok(version) if version > 0 => Some(DriverVersion::from_u32(version)),
            _ => None,
        }
    }

    /// Check if the driver is available and loaded
    pub fn is_available() -> bool {
        match Self::get_running_driver_version() {
            Ok(_) => {
                log_info!("VelociTun driver is available and loaded");
                true
            }
            Err(e) => {
                log_warn!("VelociTun driver is not available: {:?}", e);
                false
            }
        }
    }

    /// Ensure the driver is available, installing if necessary
    pub fn ensure_available() -> VelociTunResult<()> {
        if Self::is_available() {
            log_info!("VelociTun driver is already available");
            return Ok(());
        }

        log_info!("Installing VelociTun driver...");
        Self::install_from_resources()?;

        // Verify installation
        if !Self::is_available() {
            log_error!("Driver installation failed - driver not detected after installation");
            return Err(VelociTunError::DriverNotLoaded);
        }

        log_info!("VelociTun driver installed successfully");
        Ok(())
    }

    /// Force reinstall the driver
    pub fn reinstall() -> VelociTunResult<()> {
        log_info!("Reinstalling VelociTun driver...");
        Self::uninstall_if_present()?;
        Self::install_from_resources()
    }

    /// Uninstall the driver if no adapters are using it
    pub fn uninstall_if_unused() -> VelociTunResult<()> {
        // Check if any adapters are still active
        let adapters = crate::adapter::Adapter::list_all()?;
        if !adapters.is_empty() {
            log_warn!(
                "Cannot uninstall driver - {} adapters still active",
                adapters.len()
            );
            return Err(VelociTunError::SessionActive);
        }

        Self::uninstall_if_present()
    }

    /// Get detailed driver information
    pub fn info() -> VelociTunResult<DriverInfo> {
        let version = Self::version().ok_or(VelociTunError::DriverNotLoaded)?;
        let driver_path = Self::get_driver_path()?;
        let file_version = Self::get_file_version(&driver_path)?;

        Ok(DriverInfo {
            version,
            file_version,
            driver_path,
            is_loaded: Self::is_available(),
        })
    }

    fn get_running_driver_version() -> VelociTunResult<u32> {
        // Check if driver is installed in the system (not necessarily loaded)
        if !Self::is_driver_installed()? {
            return Err(VelociTunError::DriverNotLoaded);
        }

        // Get version from the installed driver package
        Self::get_installed_driver_version()
    }

    fn is_driver_installed() -> VelociTunResult<bool> {
        unsafe {
            let device_info_set =
                SetupDiCreateDeviceInfoListExW(Some(&GUID_DEVCLASS_NET), None, None, None)?;

            let mut device_info_data = SP_DEVINFO_DATA {
                cbSize: std::mem::size_of::<SP_DEVINFO_DATA>() as u32,
                ClassGuid: GUID_DEVCLASS_NET,
                DevInst: 0,
                Reserved: 0,
            };

            // Create temporary device info to query drivers
            let result = SetupDiCreateDeviceInfoW(
                device_info_set,
                windows::core::PCWSTR("VelociTun\0".encode_utf16().collect::<Vec<u16>>().as_ptr()),
                &GUID_DEVCLASS_NET,
                None,
                None,
                DICD_GENERATE_ID,
                Some(&mut device_info_data),
            );

            if result.is_err() {
                let _ = SetupDiDestroyDeviceInfoList(device_info_set);
                return Ok(false);
            }

            // Set hardware ID
            let hwid = "VelociTun\0\0".encode_utf16().collect::<Vec<u16>>();
            let hwid_bytes = std::slice::from_raw_parts(hwid.as_ptr() as *const u8, hwid.len() * 2);
            let _ = SetupDiSetDeviceRegistryPropertyW(
                device_info_set,
                &mut device_info_data,
                SPDRP_HARDWAREID,
                Some(hwid_bytes),
            );

            // Try to build driver info list - if successful, driver is installed
            let build_result = SetupDiBuildDriverInfoList(
                device_info_set,
                Some(&mut device_info_data),
                SPDIT_COMPATDRIVER,
            );

            let is_installed = build_result.is_ok();

            if is_installed {
                let _ = SetupDiDestroyDriverInfoList(
                    device_info_set,
                    Some(&device_info_data),
                    SPDIT_COMPATDRIVER,
                );
            }

            let _ = SetupDiDestroyDeviceInfoList(device_info_set);
            Ok(is_installed)
        }
    }

    fn get_installed_driver_version() -> VelociTunResult<u32> {
        use windows::Win32::Devices::DeviceAndDriverInstallation::*;

        unsafe {
            let device_info_set =
                SetupDiCreateDeviceInfoListExW(Some(&GUID_DEVCLASS_NET), None, None, None)?;

            let mut device_info_data = SP_DEVINFO_DATA {
                cbSize: std::mem::size_of::<SP_DEVINFO_DATA>() as u32,
                ClassGuid: GUID_DEVCLASS_NET,
                DevInst: 0,
                Reserved: 0,
            };

            SetupDiCreateDeviceInfoW(
                device_info_set,
                windows::core::PCWSTR("VelociTun\0".encode_utf16().collect::<Vec<u16>>().as_ptr()),
                &GUID_DEVCLASS_NET,
                None,
                None,
                DICD_GENERATE_ID,
                Some(&mut device_info_data),
            )?;

            let hwid = "VelociTun\0\0".encode_utf16().collect::<Vec<u16>>();
            let hwid_bytes = std::slice::from_raw_parts(hwid.as_ptr() as *const u8, hwid.len() * 2);
            SetupDiSetDeviceRegistryPropertyW(
                device_info_set,
                &mut device_info_data,
                SPDRP_HARDWAREID,
                Some(hwid_bytes),
            )?;

            SetupDiBuildDriverInfoList(
                device_info_set,
                Some(&mut device_info_data),
                SPDIT_COMPATDRIVER,
            )?;

            // Get first driver info
            let mut driver_info = SP_DRVINFO_DATA_V2_W {
                cbSize: std::mem::size_of::<SP_DRVINFO_DATA_V2_W>() as u32,
                ..Default::default()
            };

            SetupDiEnumDriverInfoW(
                device_info_set,
                Some(&device_info_data),
                SPDIT_COMPATDRIVER,
                0,
                &mut driver_info,
            )?;

            // Get driver detail to find INF path
            let mut detail_buffer = vec![0u8; 2048];
            let detail_data = detail_buffer.as_mut_ptr() as *mut SP_DRVINFO_DETAIL_DATA_W;
            (*detail_data).cbSize = std::mem::size_of::<SP_DRVINFO_DETAIL_DATA_W>() as u32;

            let mut size = 0;
            SetupDiGetDriverInfoDetailW(
                device_info_set,
                Some(&device_info_data),
                &driver_info,
                Some(detail_data),
                detail_buffer.len() as u32,
                Some(&mut size),
            )?;

            let _ = SetupDiDestroyDriverInfoList(
                device_info_set,
                Some(&device_info_data),
                SPDIT_COMPATDRIVER,
            );
            let _ = SetupDiDestroyDeviceInfoList(device_info_set);

            // Parse version from driver info or INF file
            // For now, return a default version - you might want to parse from INF
            Ok(0x00010000) // Version 1.0
        }
    }

    fn get_driver_path() -> VelociTunResult<String> {
        let system_dir = Self::get_system_directory()?;
        Ok(format!("{}\\drivers\\velocitun.sys", system_dir))
    }

    fn get_system_directory() -> VelociTunResult<String> {
        use windows::Win32::System::SystemInformation::GetSystemDirectoryW;

        unsafe {
            let mut buffer = vec![0u16; 260];
            let length = GetSystemDirectoryW(Some(&mut buffer));

            if length == 0 {
                return Err(VelociTunError::WindowsApi(
                    windows::core::Error::from_win32(),
                ));
            }

            buffer.truncate(length as usize);
            Ok(String::from_utf16_lossy(&buffer))
        }
    }

    fn get_file_version(path: &str) -> VelociTunResult<u32> {
        let path_wide = path
            .encode_utf16()
            .chain(std::iter::once(0))
            .collect::<Vec<u16>>();

        unsafe {
            let version_info_size =
                GetFileVersionInfoSizeW(windows::core::PCWSTR(path_wide.as_ptr()), None);

            if version_info_size == 0 {
                return Err(VelociTunError::WindowsApi(
                    windows::core::Error::from_win32(),
                ));
            }

            let mut version_info = vec![0u8; version_info_size as usize];
            GetFileVersionInfoW(
                windows::core::PCWSTR(path_wide.as_ptr()),
                0,
                version_info_size,
                version_info.as_mut_ptr() as *mut _,
            )?;

            let mut file_info: *mut VS_FIXEDFILEINFO = ptr::null_mut();
            let mut info_size = 0;

            let result = VerQueryValueW(
                version_info.as_ptr() as *const _,
                windows::core::PCWSTR("\\\\\\0".encode_utf16().collect::<Vec<u16>>().as_ptr()),
                &mut file_info as *mut _ as *mut *mut _,
                &mut info_size,
            );
            if result == FALSE {
                return Err(VelociTunError::WindowsApi(
                    windows::core::Error::from_win32(),
                ));
            }

            if file_info.is_null() {
                return Err(VelociTunError::InvalidData);
            }

            let version_ms = (*file_info).dwFileVersionMS;
            Ok(version_ms)
        }
    }

    fn install_from_resources() -> VelociTunResult<()> {
        use windows::Win32::Devices::DeviceAndDriverInstallation::*;

        let temp_dir = crate::resources::ResourceManager::create_temp_driver_directory()?;

        let inf_path = temp_dir.join("velocitun.inf");
        let sys_path = temp_dir.join("velocitun.sys");
        let cat_path = temp_dir.join("velocitun.cat");

        // Extract driver files
        crate::resources::ResourceManager::extract_driver_files(&inf_path, &sys_path, &cat_path)?;

        crate::logger::log_info!("Installing INF file: {:?}", inf_path);

        // Validate that all files exist
        if !inf_path.exists() {
            return Err(VelociTunError::InvalidParameter(
                "INF file was not extracted correctly".to_string(),
            ));
        }
        if !sys_path.exists() {
            return Err(VelociTunError::InvalidParameter(
                "SYS file was not extracted correctly".to_string(),
            ));
        }
        if !cat_path.exists() {
            return Err(VelociTunError::InvalidParameter(
                "CAT file was not extracted correctly".to_string(),
            ));
        }

        // Convert to absolute path and then to wide string
        let inf_absolute_path = inf_path.canonicalize().map_err(|e| {
            VelociTunError::InvalidParameter(format!("Failed to get absolute path: {}", e))
        })?;

        crate::logger::log_info!("Absolute INF path: {:?}", inf_absolute_path);

        let inf_path_wide = inf_absolute_path
            .to_string_lossy()
            .encode_utf16()
            .chain(std::iter::once(0))
            .collect::<Vec<u16>>();

        let result = unsafe {
            SetupCopyOEMInfW(
                windows::core::PCWSTR(inf_path_wide.as_ptr()),
                None,             // Source directory - let it auto-detect
                SPOST_NONE,       // No special processing
                SP_COPY_STYLE(0), // Default copy style
                None,             // Destination INF file name - auto-generated
                None,             // Destination INF file name size
                None,             // Required size for destination filename
            )
        };

        if result.is_err() {
            let win_error = windows::core::Error::from_win32();
            crate::logger::log_error!(
                "SetupCopyOEMInfW failed with error: {:?} (0x{:08X})",
                win_error,
                win_error.code().0
            );

            // Keep temp directory for debugging if installation fails
            crate::logger::log_error!("Keeping temporary directory for debugging: {:?}", temp_dir);

            return Err(VelociTunError::WindowsApi(win_error));
        }

        crate::logger::log_info!("Driver installation completed successfully");

        // Clean up temporary files only on success
        let cleanup_result = fs::remove_dir_all(&temp_dir);
        if cleanup_result.is_err() {
            crate::logger::log_warn!("Failed to clean up temporary directory: {:?}", temp_dir);
        }
        Ok(())
    }

    fn uninstall_if_present() -> VelociTunResult<()> {
        use windows::Win32::Devices::DeviceAndDriverInstallation::*;

        unsafe {
            let device_info_set =
                SetupDiCreateDeviceInfoListExW(Some(&GUID_DEVCLASS_NET), None, None, None)?;

            let mut device_info_data = SP_DEVINFO_DATA {
                cbSize: mem::size_of::<SP_DEVINFO_DATA>() as u32,
                ClassGuid: GUID_DEVCLASS_NET,
                DevInst: 0,
                Reserved: 0,
            };

            let hwid = "VelociTun\0\0".encode_utf16().collect::<Vec<u16>>();

            SetupDiCreateDeviceInfoW(
                device_info_set,
                windows::core::PCWSTR("VelociTun\0".encode_utf16().collect::<Vec<u16>>().as_ptr()),
                &GUID_DEVCLASS_NET,
                None,
                None,
                DICD_GENERATE_ID,
                Some(&mut device_info_data),
            )?;

            let hwid_bytes = slice::from_raw_parts(hwid.as_ptr() as *const u8, hwid.len() * 2);
            SetupDiSetDeviceRegistryPropertyW(
                device_info_set,
                &mut device_info_data,
                SPDRP_HARDWAREID,
                Some(hwid_bytes),
            )?;

            SetupDiBuildDriverInfoList(
                device_info_set,
                Some(&mut device_info_data),
                SPDIT_COMPATDRIVER,
            )?;

            // Remove all compatible drivers
            let mut enum_index = 0;
            loop {
                let mut driver_info = SP_DRVINFO_DATA_V2_W {
                    cbSize: mem::size_of::<SP_DRVINFO_DATA_V2_W>() as u32,
                    ..Default::default()
                };

                if SetupDiEnumDriverInfoW(
                    device_info_set,
                    Some(&device_info_data),
                    SPDIT_COMPATDRIVER,
                    enum_index,
                    &mut driver_info,
                )
                .is_err()
                {
                    break;
                }

                let mut detail_buffer = vec![0u8; 2048];
                let detail_data = detail_buffer.as_mut_ptr() as *mut SP_DRVINFO_DETAIL_DATA_W;
                (*detail_data).cbSize = mem::size_of::<SP_DRVINFO_DETAIL_DATA_W>() as u32;

                let mut size = 0;
                if SetupDiGetDriverInfoDetailW(
                    device_info_set,
                    Some(&device_info_data),
                    &driver_info,
                    Some(detail_data),
                    detail_buffer.len() as u32,
                    Some(&mut size),
                )
                .is_ok()
                {
                    let inf_path = slice::from_raw_parts((*detail_data).InfFileName.as_ptr(), 260);
                    let inf_path_string = String::from_utf16_lossy(inf_path);
                    let inf_path_str = inf_path_string.trim_end_matches('\0');

                    if let Some(inf_name) = path::Path::new(inf_path_str).file_name() {
                        if let Some(inf_name_str) = inf_name.to_str() {
                            let inf_name_wide = inf_name_str
                                .encode_utf16()
                                .chain(std::iter::once(0))
                                .collect::<Vec<u16>>();
                            let _ = SetupUninstallOEMInfW(
                                windows::core::PCWSTR(inf_name_wide.as_ptr()),
                                SUOI_FORCEDELETE,
                                None,
                            );
                        }
                    }
                }

                enum_index += 1;
            }

            let _ = SetupDiDestroyDriverInfoList(
                device_info_set,
                Some(&device_info_data),
                SPDIT_COMPATDRIVER,
            );
            let _ = SetupDiDestroyDeviceInfoList(device_info_set);
        }

        Ok(())
    }
}

/// Driver version information
#[derive(Debug, Clone, PartialEq, Eq)]
pub struct DriverVersion {
    pub major: u16,
    pub minor: u16,
    pub build: u16,
    pub revision: u16,
}

impl DriverVersion {
    fn from_u32(version: u32) -> Self {
        Self {
            major: ((version >> 16) & 0xFFFF) as u16,
            minor: (version & 0xFFFF) as u16,
            build: 0,
            revision: 0,
        }
    }

    pub fn as_string(&self) -> String {
        format!(
            "{}.{}.{}.{}",
            self.major, self.minor, self.build, self.revision
        )
    }
}

impl fmt::Display for DriverVersion {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(
            f,
            "{}.{}.{}.{}",
            self.major, self.minor, self.build, self.revision
        )
    }
}

/// Complete driver information
#[derive(Debug)]
pub struct DriverInfo {
    pub version: DriverVersion,
    pub file_version: u32,
    pub driver_path: String,
    pub is_loaded: bool,
}
