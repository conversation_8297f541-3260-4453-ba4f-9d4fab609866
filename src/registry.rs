use crate::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>rro<PERSON>, VelociTunResult};
use windows::{
    core::{GUI<PERSON>, HRESULT, PCWSTR},
    Win32::{
        Devices::DeviceAndDriverInstallation::{
            CM_Locate_DevNodeW, CM_MapCrToWin32Err, CM_Open_DevNode_Key, RegDisposition_OpenAlways,
            CM_LOCATE_DEVNODE_PHANTOM, CM_REGISTRY_SOFTWARE, CR_SUCCESS,
        },
        Foundation::*,
        System::{Com::CLSIDFromString, Registry::*},
    },
};

pub fn get_registry_string(hkey: HKEY, subkey: &str, value_name: &str) -> VelociTunResult<String> {
    let subkey_wide: Vec<u16> = subkey.encode_utf16().chain(std::iter::once(0)).collect();
    let value_name_wide: Vec<u16> = value_name
        .encode_utf16()
        .chain(std::iter::once(0))
        .collect();

    let mut key_handle = HKEY::default();

    unsafe {
        let result = RegOpenKeyExW(
            hkey,
            windows::core::PCWSTR(subkey_wide.as_ptr()),
            0,
            KEY_READ,
            &mut key_handle,
        );

        if result != ERROR_SUCCESS {
            return Err(VelociTunError::WindowsApi(
                windows::core::Error::from_win32(),
            ));
        }

        let mut data_type = REG_NONE;
        let mut data_size = 0u32;

        // Get the size first
        let result = RegQueryValueExW(
            key_handle,
            windows::core::PCWSTR(value_name_wide.as_ptr()),
            None,
            Some(&mut data_type),
            None,
            Some(&mut data_size),
        );

        if result != ERROR_SUCCESS {
            let _ = RegCloseKey(key_handle);
            return Err(VelociTunError::WindowsApi(
                windows::core::Error::from_win32(),
            ));
        }

        if data_type != REG_SZ && data_type != REG_EXPAND_SZ {
            let _ = RegCloseKey(key_handle);
            return Err(VelociTunError::InvalidParameter(
                "Registry value is not a string".to_string(),
            ));
        }

        let mut buffer = vec![0u16; (data_size / 2) as usize];

        let result = RegQueryValueExW(
            key_handle,
            windows::core::PCWSTR(value_name_wide.as_ptr()),
            None,
            Some(&mut data_type),
            Some(buffer.as_mut_ptr() as *mut u8),
            Some(&mut data_size),
        );

        let _ = RegCloseKey(key_handle);

        if result != ERROR_SUCCESS {
            return Err(VelociTunError::WindowsApi(
                windows::core::Error::from_win32(),
            ));
        }

        // Remove null terminator if present
        if let Some(pos) = buffer.iter().position(|&x| x == 0) {
            buffer.truncate(pos);
        }

        Ok(String::from_utf16_lossy(&buffer))
    }
}

pub fn set_registry_string(
    hkey: HKEY,
    subkey: &str,
    value_name: &str,
    value: &str,
) -> VelociTunResult<()> {
    let subkey_wide: Vec<u16> = subkey.encode_utf16().chain(std::iter::once(0)).collect();
    let value_name_wide: Vec<u16> = value_name
        .encode_utf16()
        .chain(std::iter::once(0))
        .collect();

    let mut key_handle = HKEY::default();

    unsafe {
        let result = RegCreateKeyExW(
            hkey,
            windows::core::PCWSTR(subkey_wide.as_ptr()),
            0,
            windows::core::PCWSTR::null(),
            REG_OPTION_NON_VOLATILE,
            KEY_WRITE,
            None,
            &mut key_handle,
            None,
        );

        if result != ERROR_SUCCESS {
            return Err(VelociTunError::WindowsApi(
                windows::core::Error::from_win32(),
            ));
        }

        let result = RegSetValueExW(
            key_handle,
            windows::core::PCWSTR(value_name_wide.as_ptr()),
            0,
            REG_SZ,
            Some(value.as_bytes()),
        );

        let _ = RegCloseKey(key_handle);

        if result != ERROR_SUCCESS {
            return Err(VelociTunError::WindowsApi(
                windows::core::Error::from_win32(),
            ));
        }

        Ok(())
    }
}

pub fn get_registry_dword(hkey: HKEY, subkey: &str, value_name: &str) -> VelociTunResult<u32> {
    let subkey_wide: Vec<u16> = subkey.encode_utf16().chain(std::iter::once(0)).collect();
    let value_name_wide: Vec<u16> = value_name
        .encode_utf16()
        .chain(std::iter::once(0))
        .collect();

    let mut key_handle = HKEY::default();

    unsafe {
        let result = RegOpenKeyExW(
            hkey,
            windows::core::PCWSTR(subkey_wide.as_ptr()),
            0,
            KEY_READ,
            &mut key_handle,
        );

        if result != ERROR_SUCCESS {
            return Err(VelociTunError::WindowsApi(
                windows::core::Error::from_win32(),
            ));
        }

        let mut data_type = REG_NONE;
        let mut data: u32 = 0;
        let mut data_size = std::mem::size_of::<u32>() as u32;

        let result = RegQueryValueExW(
            key_handle,
            windows::core::PCWSTR(value_name_wide.as_ptr()),
            None,
            Some(&mut data_type),
            Some(&mut data as *mut u32 as *mut u8),
            Some(&mut data_size),
        );

        let _ = RegCloseKey(key_handle);

        if result != ERROR_SUCCESS {
            return Err(VelociTunError::WindowsApi(
                windows::core::Error::from_win32(),
            ));
        }

        if data_type != REG_DWORD {
            return Err(VelociTunError::InvalidParameter(
                "Registry value is not a DWORD".to_string(),
            ));
        }

        Ok(data)
    }
}

pub fn set_registry_dword(
    hkey: HKEY,
    subkey: &str,
    value_name: &str,
    value: u32,
) -> VelociTunResult<()> {
    let subkey_wide: Vec<u16> = subkey.encode_utf16().chain(std::iter::once(0)).collect();
    let value_name_wide: Vec<u16> = value_name
        .encode_utf16()
        .chain(std::iter::once(0))
        .collect();

    let mut key_handle = HKEY::default();

    unsafe {
        let result = RegCreateKeyExW(
            hkey,
            windows::core::PCWSTR(subkey_wide.as_ptr()),
            0,
            windows::core::PCWSTR::null(),
            REG_OPTION_NON_VOLATILE,
            KEY_WRITE,
            None,
            &mut key_handle,
            None,
        );

        if result != ERROR_SUCCESS {
            return Err(VelociTunError::WindowsApi(
                windows::core::Error::from_win32(),
            ));
        }

        let result = RegSetValueExW(
            key_handle,
            windows::core::PCWSTR(value_name_wide.as_ptr()),
            0,
            REG_DWORD,
            Some(&value.to_le_bytes()),
        );

        let _ = RegCloseKey(key_handle);

        if result != ERROR_SUCCESS {
            return Err(VelociTunError::WindowsApi(
                windows::core::Error::from_win32(),
            ));
        }

        Ok(())
    }
}

pub fn delete_registry_key(hkey: HKEY, subkey: &str) -> VelociTunResult<()> {
    let subkey_wide: Vec<u16> = subkey.encode_utf16().chain(std::iter::once(0)).collect();

    unsafe {
        let result = RegDeleteKeyW(hkey, windows::core::PCWSTR(subkey_wide.as_ptr()));

        if result != ERROR_SUCCESS && result != ERROR_FILE_NOT_FOUND {
            return Err(VelociTunError::WindowsApi(
                windows::core::Error::from_win32(),
            ));
        }

        Ok(())
    }
}

/// Set SuggestedInstanceId in device registry
/// This function mimics the C implementation that sets the GUID as binary data
/// in the device's software registry key
pub fn set_suggested_instance_id(
    device_instance_id: &[u16],
    instance_id: &GUID,
) -> VelociTunResult<()> {
    unsafe {
        // Locate the device node using the phantom flag (like in C code)
        let mut dev_inst = 0u32;
        let config_ret = CM_Locate_DevNodeW(
            &mut dev_inst,
            windows::core::PCWSTR(device_instance_id.as_ptr()),
            CM_LOCATE_DEVNODE_PHANTOM,
        );

        if config_ret != CR_SUCCESS {
            let error_code = CM_MapCrToWin32Err(config_ret, 0x48); // ERROR_DEVICE_ENUMERATION_ERROR
            return Err(VelociTunError::WindowsApi(
                windows::core::Error::from_hresult(HRESULT::from_win32(error_code)),
            ));
        }

        // Open the device node's software registry key
        let mut driver_key = HKEY::default();
        let config_ret = CM_Open_DevNode_Key(
            dev_inst,
            KEY_SET_VALUE.0,
            0,
            RegDisposition_OpenAlways,
            &mut driver_key,
            CM_REGISTRY_SOFTWARE,
        );

        if config_ret != CR_SUCCESS {
            let error_code = CM_MapCrToWin32Err(config_ret, 0x259); // ERROR_PNP_REGISTRY_ERROR
            return Err(VelociTunError::WindowsApi(
                windows::core::Error::from_hresult(HRESULT::from_win32(error_code)),
            ));
        }

        // Set the SuggestedInstanceId as binary data (GUID)
        let value_name = "SuggestedInstanceId\0".encode_utf16().collect::<Vec<u16>>();
        let result = RegSetValueExW(
            driver_key,
            windows::core::PCWSTR(value_name.as_ptr()),
            0,
            REG_BINARY,
            Some(std::slice::from_raw_parts(
                instance_id as *const GUID as *const u8,
                std::mem::size_of::<GUID>(),
            )),
        );

        // Always close the key
        let _ = RegCloseKey(driver_key);

        if result != ERROR_SUCCESS {
            return Err(VelociTunError::WindowsApi(
                windows::core::Error::from_win32(),
            ));
        }

        Ok(())
    }
}

/// Query GUID from registry key handle
pub fn query_registry_guid(key: HKEY, value_name: PCWSTR) -> VelociTunResult<GUID> {
    unsafe {
        let mut data_type = REG_SZ;
        let mut data_size = 0u32;

        // First call to get size
        let result = RegQueryValueExW(
            key,
            value_name,
            None,
            Some(&mut data_type),
            None,
            Some(&mut data_size),
        );
        if result != ERROR_SUCCESS {
            return Err(VelociTunError::WindowsApi(Error::from_win32()));
        }

        // Allocate buffer and get actual data
        let mut buffer = vec![0u16; (data_size / 2) as usize];
        let result = RegQueryValueExW(
            key,
            value_name,
            None,
            Some(&mut data_type),
            Some(buffer.as_mut_ptr() as *mut u8),
            Some(&mut data_size),
        );

        if result != ERROR_SUCCESS {
            return Err(VelociTunError::WindowsApi(Error::from_win32()));
        }

        // Convert string to GUID
        let guid_str = String::from_utf16_lossy(&buffer[..data_size as usize / 2]);
        let guid = CLSIDFromString(PCWSTR(
            guid_str
                .trim_end_matches('\0')
                .encode_utf16()
                .chain(std::iter::once(0))
                .collect::<Vec<u16>>()
                .as_ptr(),
        ))
        .map_err(|e| VelociTunError::WindowsApi(e))?;

        Ok(guid)
    }
}

/// Query DWORD from registry key handle
pub fn query_registry_dword(key: HKEY, value_name: PCWSTR) -> VelociTunResult<u32> {
    unsafe {
        let mut data_type = REG_DWORD;
        let mut data_size = 4u32;
        let mut value = 0u32;

        let result = RegQueryValueExW(
            key,
            value_name,
            None,
            Some(&mut data_type),
            Some(&mut value as *mut u32 as *mut u8),
            Some(&mut data_size),
        );

        if result != ERROR_SUCCESS {
            return Err(VelociTunError::WindowsApi(Error::from_win32()));
        }

        Ok(value)
    }
}
