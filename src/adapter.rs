use std::{mem, ptr};

use crate::{logger::*, registry::*, VelociTunError, VelociTunResult};
use scopeguard::defer;
use uuid::Uuid;
use windows::{
    core::{w, Error, GUID, HRESULT, PCSTR, PCWSTR},
    Win32::{
        Devices::{
            DeviceAndDriverInstallation::{
                CM_Get_Device_IDW, CM_Locate_DevNodeW, CM_MapCrToWin32Err,
                SetupDiBuildDriverInfoList, SetupDiCallClassInstaller, SetupDiClassNameFromGuidW,
                SetupDiCreateDeviceInfoListExW, SetupDiCreateDeviceInfoW,
                SetupDiDestroyDeviceInfoList, SetupDiEnumDriverInfoW, SetupDiOpenDevRegKey,
                SetupDiOpenDeviceInfoW, SetupDiSetDeviceRegistryPropertyW,
                SetupDiSetSelectedDevice, SetupDiSetSelectedDriverW, CM_LOCATE_DEVNODE_NORMAL,
                CR_SUCCESS, DICD_GENERATE_ID, DICS_FLAG_GLOBAL, DIF_INSTALLDEVICE,
                DIF_INSTALLINTERFACES, DIF_REGISTERDEVICE, DIF_REGISTER_COINSTALLERS,
                DIOD_INHERIT_CLASSDRVS, DIREG_DRV, GUID_DEVCLASS_NET, MAX_CLASS_NAME_LEN,
                MAX_GUID_STRING_LEN, SPDIT_COMPATDRIVER, SPDRP_HARDWAREID, SP_DEVINFO_DATA,
                SP_DRVINFO_DATA_V2_W,
            },
            DeviceQuery::{
                DevCloseObjectQuery, DevCreateObjectQuery, DevObjectTypeDeviceInterface,
                DevQueryFlagUpdateResults, DevQueryResultAdd, DevQueryResultStateChange,
                DevQueryResultUpdate, DEVPROP_FILTER_EXPRESSION, DEVPROP_OPERATOR_EQUALS,
                DEVPROP_OPERATOR_EQUALS_IGNORE_CASE, DEV_QUERY_RESULT_ACTION_DATA, DEV_QUERY_STATE,
                HDEVQUERY,
            },
            Enumeration::Pnp::{
                SWDeviceCapabilitiesDriverRequired, SWDeviceCapabilitiesSilentInstall,
                SwDeviceClose, SwDeviceCreate, HSWDEVICE, SW_DEVICE_CREATE_INFO,
            },
            Properties::{
                DEVPKEY_DeviceInterface_ClassGuid, DEVPKEY_DeviceInterface_Enabled,
                DEVPKEY_Device_ClassGuid, DEVPKEY_Device_DeviceDesc, DEVPKEY_Device_FriendlyName,
                DEVPKEY_Device_InstanceId, DEVPROPCOMPKEY, DEVPROPERTY, DEVPROPID_FIRST_USABLE,
                DEVPROPKEY, DEVPROP_STORE_SYSTEM, DEVPROP_TRUE, DEVPROP_TYPE_BOOLEAN,
                DEVPROP_TYPE_GUID, DEVPROP_TYPE_STRING,
            },
        },
        Foundation::{ERROR_SUCCESS, INVALID_HANDLE_VALUE},
        NetworkManagement::{
            IpHelper::{
                ConvertInterfaceGuidToLuid, ConvertInterfaceLuidToGuid, GetAdaptersAddresses,
                GET_ADAPTERS_ADDRESSES_FLAGS, IP_ADAPTER_ADDRESSES_LH,
            },
            Ndis::NET_LUID_LH,
        },
        Networking::WinSock::AF_UNSPEC,
        System::{
            Com::StringFromGUID2,
            LibraryLoader::{FreeLibrary, GetProcAddress, LoadLibraryW},
            Registry::{RegCloseKey, KEY_QUERY_VALUE},
        },
    },
};

const HARDWARE_ID: &str = "VelociTun\0\0";

// Direct linking to nci.dll
#[link(name = "nci")]
extern "system" {
    /// Sets the connection name for a network adapter
    /// DWORD WINAPI NciSetConnectionName(const GUID *Guid, LPCWSTR NewName)
    fn NciSetConnectionName(guid: *const GUID, new_name: PCWSTR) -> u32;
}

/// Callback function to track SwDeviceCreate completion
unsafe extern "system" fn device_creation_callback(
    _sw_device: HSWDEVICE,
    result: HRESULT,
    context: *const std::ffi::c_void,
    _device_instance_id: PCWSTR,
) {
    if context.is_null() {
        return;
    }

    unsafe {
        let tx = &*(context as *const std::sync::mpsc::Sender<VelociTunResult<()>>);

        if result.is_ok() {
            log_info!("Device created successfully via callback");
            let _ = tx.send(Ok(()));
        } else {
            log_error!("Device creation failed via callback: {:?}", result);
            let _ = tx.send(Err(VelociTunError::WindowsApi(
                windows::core::Error::from_hresult(result),
            )));
        }
    }
}

/// Windows version detection
fn is_windows_7() -> bool {
    use windows::Win32::System::SystemInformation::{GetVersionExW, OSVERSIONINFOW};

    unsafe {
        let mut version_info = OSVERSIONINFOW {
            dwOSVersionInfoSize: mem::size_of::<OSVERSIONINFOW>() as u32,
            ..Default::default()
        };

        if GetVersionExW(&mut version_info).is_ok() {
            version_info.dwMajorVersion == 6 && version_info.dwMinorVersion == 1
        } else {
            false
        }
    }
}

fn is_windows_10_or_later() -> bool {
    use windows::Win32::System::SystemInformation::{GetVersionExW, OSVERSIONINFOW};

    unsafe {
        let mut version_info = OSVERSIONINFOW {
            dwOSVersionInfoSize: mem::size_of::<OSVERSIONINFOW>() as u32,
            ..Default::default()
        };

        if GetVersionExW(&mut version_info).is_ok() {
            version_info.dwMajorVersion >= 10
        } else {
            false
        }
    }
}

const DEVPKEY_VELOCITUNE_NAME: DEVPROPKEY = DEVPROPKEY {
    // 134dc0ba-57da-4d75-82fd-0e1e025ea7b5
    fmtid: GUID::from_values(
        0x134dc0ba,
        0x57da,
        0x4d75,
        [0x82, 0xfd, 0x0e, 0x1e, 0x02, 0x5e, 0xa7, 0xb5],
    ),
    pid: DEVPROPID_FIRST_USABLE + 1,
};

/// VelociTun network adapter representation
pub struct Adapter {
    pub(crate) luid: NET_LUID_LH,
    pub(crate) name: String,
    pub(crate) tunnel_type: String,
    pub(crate) guid: GUID,
    pub(crate) cfg_instance_id: GUID,
    pub(crate) luid_index: u32,
    pub(crate) if_type: u32,
    pub(crate) device_instance_id: String,
    pub(crate) interface_filename: Option<String>,
}

/// Builder for creating VelociTun adapters
#[derive(Debug)]
pub struct AdapterBuilder {
    name: Option<String>,
    tunnel_type: String,
    guid: Option<GUID>,
}

impl AdapterBuilder {
    /// Create a new adapter builder
    pub fn new() -> Self {
        Self {
            name: None,
            tunnel_type: "VelociTun".to_string(),
            guid: None,
        }
    }

    /// Set the adapter name
    pub fn name<S: Into<String>>(mut self, name: S) -> Self {
        self.name = Some(name.into());
        self
    }

    /// Set the tunnel type (default: "VelociTun")
    pub fn tunnel_type<S: Into<String>>(mut self, tunnel_type: S) -> Self {
        self.tunnel_type = tunnel_type.into();
        self
    }

    /// Set a specific GUID (optional, will generate random if not set)
    pub fn guid(mut self, guid: GUID) -> Self {
        self.guid = Some(guid);
        self
    }

    /// Build the adapter
    pub fn build(self) -> VelociTunResult<Adapter> {
        let name = self
            .name
            .unwrap_or_else(|| format!("VelociTun-{}", Uuid::new_v4().simple()));
        let guid = self.guid.unwrap_or_else(Self::generate_guid);

        log_info!("Creating adapter '{}' with GUID {:?}", name, guid);

        // Ensure driver is available
        crate::driver::Driver::ensure_available()?;

        log_info!("Creating network adapter...");

        // Create the network adapter
        let adapter = Self::create_network_adapter(&name, &self.tunnel_type, &guid)?;

        Ok(adapter)
    }

    fn generate_guid() -> GUID {
        GUID::new().unwrap()
    }

    fn create_network_adapter(
        name: &str,
        description: &str,
        guid: &GUID,
    ) -> VelociTunResult<Adapter> {
        log_info!("Creating network adapter for Windows version");

        // Check Windows version and use appropriate method
        if is_windows_7() {
            log_info!("Using Windows 7 adapter creation method");
            Self::create_adapter_win7(name, description, guid)
        } else {
            log_info!("Using modern adapter creation method with SwDeviceCreate");
            Self::create_adapter_modern(name, description, guid)
        }
    }

    fn create_adapter_win7(name: &str, description: &str, guid: &GUID) -> VelociTunResult<Adapter> {
        // Windows 7 uses the traditional SetupAPI method without SwDeviceCreate
        Self::create_adapter_setupapi(name, description, guid)
    }

    /// Complete device setup after creation (common for both Win7 and modern)
    fn complete_device_setup(
        name: &str,
        description: &str,
        guid: &GUID,
        device_instance_id: &str,
    ) -> VelociTunResult<Adapter> {
        log_info!("Completing device setup for: {}", device_instance_id);

        // Create device info set for SetupAPI operations
        let device_info_set =
            unsafe { SetupDiCreateDeviceInfoListExW(Some(&GUID_DEVCLASS_NET), None, None, None) }?;

        defer! {
            _ = unsafe {SetupDiDestroyDeviceInfoList(device_info_set)};
        }

        // Open device info using device instance ID
        let mut device_info_data = SP_DEVINFO_DATA {
            cbSize: mem::size_of::<SP_DEVINFO_DATA>() as u32,
            ClassGuid: GUID_DEVCLASS_NET,
            DevInst: 0,
            Reserved: 0,
        };

        let device_instance_id_wide: Vec<u16> = device_instance_id
            .encode_utf16()
            .chain(std::iter::once(0))
            .collect();

        log_info!(
            "Opening device info for instance ID: {}",
            device_instance_id
        );
        unsafe {
            SetupDiOpenDeviceInfoW(
                device_info_set,
                PCWSTR(device_instance_id_wide.as_ptr()),
                None,
                DIOD_INHERIT_CLASSDRVS,
                Some(&mut device_info_data),
            )
        }?;

        // Populate adapter data (equivalent to PopulateAdapterData in C)
        let (cfg_instance_id, luid_index, if_type, interface_filename) =
            Self::populate_adapter_data(device_info_set, &device_info_data)?;

        log_info!("Device opened successfully via SetupAPI");

        // Build final adapter structure
        let luid = NET_LUID_LH {
            Value: ((if_type as u64) << 48) | (luid_index as u64),
        };

        let adapter = Adapter {
            luid,
            name: name.to_string(),
            tunnel_type: description.to_string(),
            guid: *guid,
            cfg_instance_id,
            luid_index,
            if_type,
            device_instance_id: device_instance_id.to_string(),
            interface_filename,
        };

        // Set adapter name via NCI (Network Configuration Interface)
        Self::set_adapter_name(&cfg_instance_id, name)?;

        log_info!("Adapter setup completed successfully for: {}", name);
        Ok(adapter)
    }

    /// Populate adapter data from registry (equivalent to PopulateAdapterData in C)
    fn populate_adapter_data(
        device_info_set: windows::Win32::Devices::DeviceAndDriverInstallation::HDEVINFO,
        device_info_data: &SP_DEVINFO_DATA,
    ) -> VelociTunResult<(GUID, u32, u32, Option<String>)> {
        unsafe {
            // Open device registry key
            let key = SetupDiOpenDevRegKey(
                device_info_set,
                device_info_data,
                DICS_FLAG_GLOBAL.0,
                0,
                DIREG_DRV,
                KEY_QUERY_VALUE.0,
            )?;

            defer! {
                _ = RegCloseKey(key);
            }

            // Get NetCfgInstanceId
            let cfg_instance_id =
                crate::registry::query_registry_guid(key, w!("NetCfgInstanceId"))?;

            // Get NetLuidIndex
            let luid_index = crate::registry::query_registry_dword(key, w!("NetLuidIndex"))?;

            // Get *IfType
            let if_type = crate::registry::query_registry_dword(key, w!("*IfType"))?;

            // Get interface filename
            let device_instance_id_str = format!(
                "\\\\?\\{}",
                String::from_utf16_lossy(&Self::get_device_instance_id(device_info_data.DevInst)?)
            );
            let interface_filename = Some(device_instance_id_str);

            Ok((cfg_instance_id, luid_index, if_type, interface_filename))
        }
    }

    /// Get device instance ID
    fn get_device_instance_id(dev_inst: u32) -> VelociTunResult<Vec<u16>> {
        unsafe {
            let mut buffer = [0u16; 200];
            let result = CM_Get_Device_IDW(dev_inst, &mut buffer, 0);
            if result != CR_SUCCESS {
                let error = CM_MapCrToWin32Err(result, 0);
                let source = Error::from_hresult(HRESULT::from_win32(error));
                return Err(VelociTunError::WindowsApi(source));
            }
            Ok(buffer.to_vec())
        }
    }

    /// Set adapter name via direct NCI API call (preferred method)
    fn set_adapter_name(cfg_instance_id: &GUID, name: &str) -> VelociTunResult<()> {
        log_info!(
            "Setting adapter name '{}' for GUID {:?}",
            name,
            cfg_instance_id
        );

        // Use direct NCI API call (no dynamic loading needed)
        match Self::set_adapter_name_nci_direct(cfg_instance_id, name) {
            Ok(()) => {
                log_info!("Successfully set adapter name via direct NCI call");
                Ok(())
            }
            Err(e) => {
                log_warn!("Direct NCI method failed: {:?}", e);
                // This is not a critical failure - the device is still created
                Ok(())
            }
        }
    }

    /// Set adapter name via Windows Registry (preferred method)
    fn set_adapter_name_registry(cfg_instance_id: &GUID, name: &str) -> VelociTunResult<()> {
        unsafe {
            // Convert GUID to string
            let mut guid_str = [0u16; 39]; // {xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx}
            let ret = StringFromGUID2(cfg_instance_id, &mut guid_str);
            if ret == 0 {
                return Err(VelociTunError::InvalidParameter(format!(
                    "{cfg_instance_id:?}"
                )));
            }

            let guid_string = String::from_utf16_lossy(&guid_str).trim_end_matches('\0');

            // Registry path for network connections
            let reg_path = format!(
                "SYSTEM\\CurrentControlSet\\Control\\Network\\{{4D36E972-E325-11CE-BFC1-08002BE10318}}\\{}\\Connection",
                guid_string
            );

            let reg_path_wide: Vec<u16> =
                reg_path.encode_utf16().chain(std::iter::once(0)).collect();

            // Open registry key
            let mut key = windows::Win32::System::Registry::HKEY::default();
            let result = RegOpenKeyExW(
                HKEY_LOCAL_MACHINE,
                PCWSTR(reg_path_wide.as_ptr()),
                0,
                KEY_SET_VALUE,
                &mut key,
            );

            if result.is_err() {
                return Err(VelociTunError::WindowsApi(
                    windows::core::Error::from_win32(),
                ));
            }

            defer! {
                _ = RegCloseKey(key);
            }

            // Set the Name value
            let name_wide: Vec<u16> = name.encode_utf16().chain(std::iter::once(0)).collect();
            let name_key: Vec<u16> = "Name\0".encode_utf16().collect();

            let result = RegSetValueExW(
                key,
                PCWSTR(name_key.as_ptr()),
                0,
                REG_SZ,
                Some(std::slice::from_raw_parts(
                    name_wide.as_ptr() as *const u8,
                    name_wide.len() * 2,
                )),
            );

            if result.is_err() {
                return Err(VelociTunError::WindowsApi(
                    windows::core::Error::from_win32(),
                ));
            }

            log_info!("Successfully set adapter name in registry");
            Ok(())
        }
    }

    /// Set adapter name via direct NCI API call (no dynamic loading)
    fn set_adapter_name_nci_direct(cfg_instance_id: &GUID, name: &str) -> VelociTunResult<()> {
        unsafe {
            // Convert name to wide string
            let name_wide: Vec<u16> = name.encode_utf16().chain(std::iter::once(0)).collect();

            // Call the directly linked NciSetConnectionName function
            let result = NciSetConnectionName(cfg_instance_id, PCWSTR(name_wide.as_ptr()));

            if result != 0 {
                return Err(VelociTunError::WindowsApi(
                    windows::core::Error::from_win32(),
                ));
            }

            Ok(())
        }
    }

    fn create_adapter_modern(
        name: &str,
        description: &str,
        guid: &GUID,
    ) -> VelociTunResult<Adapter> {
        let hwid: Vec<u16> = HARDWARE_ID.encode_utf16().collect();

        unsafe {
            let mut root_node = 0;
            let ret = CM_Locate_DevNodeW(&mut root_node, None, CM_LOCATE_DEVNODE_NORMAL);
            if ret != CR_SUCCESS {
                let error = CM_MapCrToWin32Err(ret, 0);
                let source = Error::from_hresult(HRESULT::from_win32(error));
                log_error!("Failed to locate root node: {:?}", source);
                return Err(VelociTunError::WindowsApi(source));
            }

            let mut root_node_name = [0; 200];
            let ret = CM_Get_Device_IDW(root_node, &mut root_node_name, 0);
            if ret != CR_SUCCESS {
                let error = CM_MapCrToWin32Err(ret, 0);
                let source = Error::from_hresult(HRESULT::from_win32(error));
                log_error!("Failed to get root node name: {:?}", source);
                return Err(VelociTunError::WindowsApi(source));
            }

            let mut instance_id_str = [0; MAX_GUID_STRING_LEN as _];
            let ret = StringFromGUID2(guid, &mut instance_id_str);
            if ret == 0 {
                log_error!("Failed to convert GUID to string");
                return Err(VelociTunError::InvalidParameter(format!("{guid:?}",)));
            }

            // For Windows 10+, create stub device first if needed
            if is_windows_10_or_later() {
                log_info!("Creating stub device for Windows 10+");

                // Create a temporary device instance ID for the stub device
                let temp_device_instance_id = format!(
                    "ROOT\\VelociTun\\{}",
                    String::from_utf16_lossy(&instance_id_str).trim_end_matches('\0')
                );

                Self::create_stub_device(
                    &hwid,
                    &root_node_name,
                    &instance_id_str,
                    description,
                    &guid,
                    &temp_device_instance_id,
                )?;
            }

            // Create the main device
            let create_info = SW_DEVICE_CREATE_INFO {
                cbSize: mem::size_of::<SW_DEVICE_CREATE_INFO>() as u32,
                pszInstanceId: PCWSTR(instance_id_str.as_ptr()),
                pszzHardwareIds: PCWSTR(hwid.as_ptr()),
                CapabilityFlags: SWDeviceCapabilitiesSilentInstall.0 as u32
                    | SWDeviceCapabilitiesDriverRequired.0 as u32,
                pszDeviceDescription: PCWSTR(
                    description.encode_utf16().collect::<Vec<u16>>().as_ptr(),
                ),
                pContainerId: ptr::null(),
                pszzCompatibleIds: PCWSTR::null(),
                pszDeviceLocation: PCWSTR::null(),
                pSecurityDescriptor: ptr::null(),
            };

            let name_utf16: Vec<u16> = name.encode_utf16().chain(std::iter::once(0)).collect();
            let desc_utf16: Vec<u16> = description
                .encode_utf16()
                .chain(std::iter::once(0))
                .collect();

            let device_properties = [
                DEVPROPERTY {
                    CompKey: DEVPROPCOMPKEY {
                        Key: DEVPKEY_VELOCITUNE_NAME,
                        Store: DEVPROP_STORE_SYSTEM,
                        LocaleName: PCWSTR::null(),
                    },
                    Type: DEVPROP_TYPE_STRING,
                    BufferSize: (name_utf16.len() * 2) as u32,
                    Buffer: name_utf16.as_ptr() as _,
                },
                DEVPROPERTY {
                    CompKey: DEVPROPCOMPKEY {
                        Key: DEVPKEY_Device_FriendlyName,
                        Store: DEVPROP_STORE_SYSTEM,
                        LocaleName: PCWSTR::null(),
                    },
                    Type: DEVPROP_TYPE_STRING,
                    BufferSize: (desc_utf16.len() * 2) as u32,
                    Buffer: desc_utf16.as_ptr() as _,
                },
                DEVPROPERTY {
                    CompKey: DEVPROPCOMPKEY {
                        Key: DEVPKEY_Device_DeviceDesc,
                        Store: DEVPROP_STORE_SYSTEM,
                        LocaleName: PCWSTR::null(),
                    },
                    Type: DEVPROP_TYPE_STRING,
                    BufferSize: (desc_utf16.len() * 2) as u32,
                    Buffer: desc_utf16.as_ptr() as _,
                },
            ];

            log_info!("Creating SwDevice...");

            // Create a channel for communication between callback and main thread
            let (tx, rx) = std::sync::mpsc::channel::<VelociTunResult<()>>();

            // Box the sender to pass it as context
            let tx_boxed = Box::new(tx);
            let context_ptr = Box::into_raw(tx_boxed) as *const std::ffi::c_void;

            let sw_device = SwDeviceCreate(
                PCWSTR(hwid.as_ptr()),
                PCWSTR(root_node_name.as_ptr()),
                &create_info,
                Some(&device_properties),
                Some(device_creation_callback),
                Some(context_ptr),
            )?;

            defer! {
                _ = SwDeviceClose(sw_device);
            }

            // Wait for callback result with timeout
            let timeout = std::time::Duration::from_secs(30);
            match rx.recv_timeout(timeout) {
                Ok(result) => {
                    // Clean up context
                    let _ = Box::from_raw(
                        context_ptr as *mut std::sync::mpsc::Sender<VelociTunResult<()>>,
                    );

                    result?; // Propagate any callback error
                    log_info!("SwDevice creation confirmed via callback");
                }
                Err(std::sync::mpsc::RecvTimeoutError::Timeout) => {
                    log_warn!("SwDevice creation callback timeout after 30 seconds");
                    // Clean up context
                    let _ = Box::from_raw(
                        context_ptr as *mut std::sync::mpsc::Sender<VelociTunResult<()>>,
                    );
                    // Continue anyway - device might still be created
                }
                Err(std::sync::mpsc::RecvTimeoutError::Disconnected) => {
                    log_error!("SwDevice creation callback channel disconnected");
                    // Clean up context
                    let _ = Box::from_raw(
                        context_ptr as *mut std::sync::mpsc::Sender<VelociTunResult<()>>,
                    );
                    return Err(VelociTunError::AdapterNotFound);
                }
            }

            log_info!("SwDevice created successfully");

            // Wait for interface to be available
            Self::wait_for_interface(&instance_id_str)?;

            // Complete device setup using the common function
            let device_instance_id = String::from_utf16_lossy(&instance_id_str)
                .trim_end_matches('\0')
                .to_string();
            Self::complete_device_setup(name, description, guid, &device_instance_id)
        }
    }

    fn create_stub_device(
        hwid: &[u16],
        root_node_name: &[u16],
        instance_id_str: &[u16],
        description: &str,
        guid: &GUID,
        device_instance_id: &str,
    ) -> VelociTunResult<()> {
        unsafe {
            let stub_create_info = SW_DEVICE_CREATE_INFO {
                cbSize: mem::size_of::<SW_DEVICE_CREATE_INFO>() as u32,
                pszInstanceId: PCWSTR(instance_id_str.as_ptr()),
                pszzHardwareIds: PCWSTR::null(), // Empty for stub
                CapabilityFlags: SWDeviceCapabilitiesSilentInstall.0 as u32
                    | SWDeviceCapabilitiesDriverRequired.0 as u32,
                pszDeviceDescription: PCWSTR(
                    description.encode_utf16().collect::<Vec<u16>>().as_ptr(),
                ),
                pContainerId: ptr::null(),
                pszzCompatibleIds: PCWSTR::null(),
                pszDeviceLocation: PCWSTR::null(),
                pSecurityDescriptor: ptr::null(),
            };

            let stub_properties = [DEVPROPERTY {
                CompKey: DEVPROPCOMPKEY {
                    Key: DEVPKEY_Device_ClassGuid,
                    Store: DEVPROP_STORE_SYSTEM,
                    LocaleName: PCWSTR::null(),
                },
                Type: DEVPROP_TYPE_GUID,
                BufferSize: mem::size_of::<GUID>() as u32,
                Buffer: &GUID_DEVCLASS_NET as *const _ as _,
            }];

            log_info!("Creating stub device...");

            // Create a channel for communication between callback and main thread
            let (tx, rx) = std::sync::mpsc::channel::<VelociTunResult<()>>();

            // Box the sender to pass it as context
            let tx_boxed = Box::new(tx);
            let context_ptr = Box::into_raw(tx_boxed) as *const std::ffi::c_void;

            let stub_device = SwDeviceCreate(
                PCWSTR(hwid.as_ptr()),
                PCWSTR(root_node_name.as_ptr()),
                &stub_create_info,
                Some(&stub_properties),
                Some(device_creation_callback),
                Some(context_ptr),
            )?;

            defer! {
                _ = SwDeviceClose(stub_device);
            }

            // Wait for callback result with timeout
            let timeout = std::time::Duration::from_secs(15);
            match rx.recv_timeout(timeout) {
                Ok(result) => {
                    // Clean up context
                    let _ = Box::from_raw(
                        context_ptr as *mut std::sync::mpsc::Sender<VelociTunResult<()>>,
                    );

                    result?; // Propagate any callback error
                    log_info!("Stub device creation confirmed via callback");
                }
                Err(std::sync::mpsc::RecvTimeoutError::Timeout) => {
                    log_warn!("Stub device creation callback timeout after 15 seconds");
                    // Clean up context
                    let _ = Box::from_raw(
                        context_ptr as *mut std::sync::mpsc::Sender<VelociTunResult<()>>,
                    );
                    // Continue anyway - device might still be created
                }
                Err(std::sync::mpsc::RecvTimeoutError::Disconnected) => {
                    log_error!("Stub device creation callback channel disconnected");
                    // Clean up context
                    let _ = Box::from_raw(
                        context_ptr as *mut std::sync::mpsc::Sender<VelociTunResult<()>>,
                    );
                    return Err(VelociTunError::AdapterNotFound);
                }
            }

            log_info!("Stub device created successfully");

            // Set SuggestedInstanceId in device registry (matching C implementation)
            log_info!("Setting SuggestedInstanceId in device registry...");

            // Convert device instance ID to null-terminated wide string
            let device_instance_id_wide: Vec<u16> = device_instance_id
                .encode_utf16()
                .chain(std::iter::once(0))
                .collect();

            match set_suggested_instance_id(&device_instance_id_wide, guid) {
                Ok(()) => {
                    log_info!("SuggestedInstanceId set successfully");
                }
                Err(e) => {
                    log_warn!("Failed to set SuggestedInstanceId: {:?}", e);
                    // Don't fail the entire operation, just log the warning
                    // This matches the behavior where registry operations are not critical
                }
            }

            Ok(())
        }
    }

    fn wait_for_interface(instance_id: &[u16]) -> VelociTunResult<()> {
        // For Windows 7, we don't need to wait
        if is_windows_7() {
            return Ok(());
        }

        log_info!("Waiting for interface to be available using DevQuery API...");

        // Convert instance_id to string for logging
        let instance_str = String::from_utf16_lossy(instance_id);
        log_info!("Waiting for interface with instance ID: {}", instance_str);

        // Use DevQuery API for more efficient waiting
        match Self::wait_for_interface_devquery(instance_id) {
            Ok(()) => {
                log_info!("Interface became available via DevQuery API");
                Ok(())
            }
            Err(e) => {
                log_warn!("DevQuery API failed, falling back to polling: {:?}", e);
                Self::wait_for_interface_polling(instance_id)
            }
        }
    }

    #[allow(non_upper_case_globals)]
    fn wait_for_interface_devquery(instance_id: &[u16]) -> VelociTunResult<()> {
        use std::{sync::mpsc, time::Duration};

        log_info!("Using DevQuery API with filters to wait for interface...");

        unsafe {
            // Convert instance_id to null-terminated string
            let instance_str: Vec<u16> = instance_id
                .iter()
                .cloned()
                .chain(std::iter::once(0))
                .collect();

            // Create filters based on the original C implementation
            let dev_prop_true = DEVPROP_TRUE;
            let guid_devinterface_net = GUID_DEVCLASS_NET;

            let filters = [
                // Filter 1: Device Instance ID (case-insensitive match)
                DEVPROP_FILTER_EXPRESSION {
                    Operator: DEVPROP_OPERATOR_EQUALS_IGNORE_CASE,
                    Property: DEVPROPERTY {
                        CompKey: DEVPROPCOMPKEY {
                            Key: DEVPKEY_Device_InstanceId,
                            Store: DEVPROP_STORE_SYSTEM,
                            LocaleName: PCWSTR::null(),
                        },
                        Type: DEVPROP_TYPE_STRING,
                        BufferSize: (instance_str.len() * mem::size_of::<u16>()) as u32,
                        Buffer: instance_str.as_ptr() as *mut _,
                    },
                },
                // Filter 2: Device Interface Enabled
                DEVPROP_FILTER_EXPRESSION {
                    Operator: DEVPROP_OPERATOR_EQUALS,
                    Property: DEVPROPERTY {
                        CompKey: DEVPROPCOMPKEY {
                            Key: DEVPKEY_DeviceInterface_Enabled,
                            Store: DEVPROP_STORE_SYSTEM,
                            LocaleName: PCWSTR::null(),
                        },
                        Type: DEVPROP_TYPE_BOOLEAN,
                        BufferSize: mem::size_of::<u8>() as u32,
                        Buffer: &dev_prop_true as *const _ as *mut _,
                    },
                },
                // Filter 3: Device Interface Class GUID (Network)
                DEVPROP_FILTER_EXPRESSION {
                    Operator: DEVPROP_OPERATOR_EQUALS,
                    Property: DEVPROPERTY {
                        CompKey: DEVPROPCOMPKEY {
                            Key: DEVPKEY_DeviceInterface_ClassGuid,
                            Store: DEVPROP_STORE_SYSTEM,
                            LocaleName: PCWSTR::null(),
                        },
                        Type: DEVPROP_TYPE_GUID,
                        BufferSize: mem::size_of::<GUID>() as u32,
                        Buffer: &guid_devinterface_net as *const _ as *mut _,
                    },
                },
            ];

            // Create channel for communication between callback and main thread
            let (tx, rx) = mpsc::channel::<VelociTunResult<()>>();

            // Define callback function with channel
            extern "system" fn query_callback(
                _query_handle: HDEVQUERY,
                context: *const std::ffi::c_void,
                action_data: *const DEV_QUERY_RESULT_ACTION_DATA,
            ) {
                if action_data.is_null() || context.is_null() {
                    return;
                }

                unsafe {
                    let action_data = &*action_data;
                    let tx = &*(context as *const mpsc::Sender<VelociTunResult<()>>);

                    match action_data.Action {
                        DevQueryResultStateChange => {
                            // Check if query was aborted
                            if action_data.Data.State == DEV_QUERY_STATE(2) {
                                // DevQueryStateAborted
                                log_warn!("DevQuery: Query was aborted");
                                let _ = tx.send(Err(VelociTunError::AdapterNotFound));
                            }
                        }
                        DevQueryResultAdd | DevQueryResultUpdate => {
                            log_info!("DevQuery: Target device interface found!");
                            let _ = tx.send(Ok(()));
                        }
                        _ => {
                            // Ignore other actions
                        }
                    }
                }
            }

            // Create context for callback (the sender)
            let tx_boxed = Box::new(tx);
            let context_ptr = Box::into_raw(tx_boxed) as *const std::ffi::c_void;

            // Create the query with filters
            let result = DevCreateObjectQuery(
                DevObjectTypeDeviceInterface,
                DevQueryFlagUpdateResults.0 as u32,
                None,           // No requested properties needed with filters
                Some(&filters), // Use our filters
                Some(query_callback),
                Some(context_ptr),
            );

            match result {
                Ok(query_handle) => {
                    log_info!(
                        "DevQuery created successfully with filters, waiting for interface..."
                    );

                    defer! {
                        _ = DevCloseObjectQuery(query_handle);
                    }

                    // Wait for callback result with timeout
                    let timeout = Duration::from_secs(15); // Match original C implementation
                    match rx.recv_timeout(timeout) {
                        Ok(result) => {
                            // Clean up context
                            let _ = Box::from_raw(
                                context_ptr as *mut mpsc::Sender<VelociTunResult<()>>,
                            );

                            match result {
                                Ok(()) => {
                                    log_info!("Interface found via DevQuery API with filters");
                                    Ok(())
                                }
                                Err(e) => {
                                    log_error!("DevQuery reported error: {:?}", e);
                                    Err(e)
                                }
                            }
                        }
                        Err(mpsc::RecvTimeoutError::Timeout) => {
                            log_warn!("DevQuery API timeout after 15 seconds");
                            // Clean up context
                            let _ = Box::from_raw(
                                context_ptr as *mut mpsc::Sender<VelociTunResult<()>>,
                            );
                            Err(VelociTunError::Timeout)
                        }
                        Err(mpsc::RecvTimeoutError::Disconnected) => {
                            log_error!("DevQuery callback channel disconnected");
                            // Clean up context
                            let _ = Box::from_raw(
                                context_ptr as *mut mpsc::Sender<VelociTunResult<()>>,
                            );
                            Err(VelociTunError::AdapterNotFound)
                        }
                    }
                }
                Err(e) => {
                    log_error!("Failed to create DevQuery: {:?}", e);
                    // Clean up context
                    let _ = Box::from_raw(context_ptr as *mut mpsc::Sender<VelociTunResult<()>>);
                    Err(VelociTunError::WindowsApi(e))
                }
            }
        }
    }

    fn wait_for_interface_polling(instance_id: &[u16]) -> VelociTunResult<()> {
        log_info!("Using polling mechanism to wait for interface...");

        // Use polling mechanism to check if interface is available
        let max_attempts = 50; // 5 seconds total (50 * 100ms)
        let poll_interval = std::time::Duration::from_millis(100);

        for attempt in 1..=max_attempts {
            if Self::check_interface_available(instance_id)? {
                log_info!("Interface became available after {} attempts", attempt);
                return Ok(());
            }

            if attempt % 10 == 0 {
                log_info!(
                    "Still waiting for interface... (attempt {}/{})",
                    attempt,
                    max_attempts
                );
            }

            std::thread::sleep(poll_interval);
        }

        log_warn!("Interface did not become available within timeout");
        // Don't fail here - continue and let the LUID search handle it
        Ok(())
    }

    fn check_interface_available(instance_id: &[u16]) -> VelociTunResult<bool> {
        use windows::Win32::NetworkManagement::IpHelper::*;

        unsafe {
            let mut size = 0;
            GetAdaptersAddresses(
                AF_UNSPEC.0 as u32,
                GET_ADAPTERS_ADDRESSES_FLAGS(0),
                None,
                None,
                &mut size,
            );

            if size == 0 {
                return Ok(false);
            }

            let mut buffer = vec![0u8; size as usize];
            let adapters = buffer.as_mut_ptr() as *mut IP_ADAPTER_ADDRESSES_LH;

            let result = GetAdaptersAddresses(
                AF_UNSPEC.0 as u32,
                GET_ADAPTERS_ADDRESSES_FLAGS(0),
                None,
                Some(adapters),
                &mut size,
            );

            if result != 0 {
                return Ok(false); // Don't fail, just return false
            }

            let instance_str = String::from_utf16_lossy(instance_id);
            let mut current = adapters;

            while !current.is_null() {
                let adapter = &*current;

                // Check if this adapter matches our instance ID
                if !adapter.AdapterName.is_null() {
                    let adapter_name = std::slice::from_raw_parts(
                        adapter.AdapterName.0 as *const u8,
                        Self::strlen_safe(adapter.AdapterName.0 as *const i8),
                    );
                    let adapter_name_str = String::from_utf8_lossy(adapter_name);

                    // Check if the adapter name contains our instance ID (simplified check)
                    if instance_str.contains(adapter_name_str.as_ref())
                        || adapter_name_str.contains(&instance_str)
                    {
                        log_info!("Found matching adapter: {}", adapter_name_str);
                        return Ok(true);
                    }
                }

                // Also check friendly name
                if !adapter.FriendlyName.is_null() {
                    let friendly_name = std::slice::from_raw_parts(
                        adapter.FriendlyName.0,
                        Self::wcslen_safe(adapter.FriendlyName.0),
                    );
                    let friendly_name_str = String::from_utf16_lossy(friendly_name);

                    if friendly_name_str.contains("VelociTun") {
                        log_info!(
                            "Found VelociTun adapter by friendly name: {}",
                            friendly_name_str
                        );
                        return Ok(true);
                    }
                }

                current = adapter.Next;
            }

            Ok(false)
        }
    }

    fn strlen_safe(ptr: *const i8) -> usize {
        if ptr.is_null() {
            return 0;
        }

        unsafe {
            let mut len = 0;
            let mut current = ptr;
            while *current != 0 && len < 1024 {
                // Safety limit
                current = current.add(1);
                len += 1;
            }
            len
        }
    }

    fn create_adapter_setupapi(
        name: &str,
        description: &str,
        guid: &GUID,
    ) -> VelociTunResult<Adapter> {
        unsafe {
            let mut device_name = vec![0; MAX_CLASS_NAME_LEN as usize];
            let mut required_size = 0;

            SetupDiClassNameFromGuidW(
                &GUID_DEVCLASS_NET,
                &mut device_name,
                Some(&mut required_size),
            )?;

            let device_info_set =
                match SetupDiCreateDeviceInfoListExW(Some(&GUID_DEVCLASS_NET), None, None, None) {
                    Ok(device_info_set) => device_info_set,
                    Err(err) => {
                        log_error!("Failed to create device info set: {}", err);
                        return Err(VelociTunError::WindowsApi(err));
                    }
                };

            // Device info set will be cleaned up automatically when it goes out of scope

            let mut device_info_data = SP_DEVINFO_DATA {
                cbSize: std::mem::size_of::<SP_DEVINFO_DATA>() as u32,
                ClassGuid: GUID_DEVCLASS_NET,
                DevInst: 0,
                Reserved: 0,
            };

            log_info!("Creating device info...");
            SetupDiCreateDeviceInfoW(
                device_info_set,
                PCWSTR(device_name[..required_size as usize].as_ptr()),
                &GUID_DEVCLASS_NET,
                None,
                None,
                DICD_GENERATE_ID,
                Some(&mut device_info_data),
            )?;

            SetupDiSetSelectedDevice(device_info_set, &device_info_data as *const _ as _)?;

            log_info!("Setting device registry property...");
            let hwid: Vec<u16> = "VelociTun\0\0".encode_utf16().collect();
            let buf = std::slice::from_raw_parts(hwid.as_ptr() as *const u8, hwid.len() * 2);
            SetupDiSetDeviceRegistryPropertyW(
                device_info_set,
                &mut device_info_data,
                SPDRP_HARDWAREID,
                Some(buf),
            )?;

            log_info!("Building driver info list...");
            SetupDiBuildDriverInfoList(
                device_info_set,
                Some(&mut device_info_data),
                SPDIT_COMPATDRIVER,
            )?;

            log_info!("Selecting driver...");

            // Select the first available driver
            // let mut driver_info: SP_DRVINFO_DATA_V2_W = mem::zeroed();
            // driver_info.cbSize = mem::size_of_val(&driver_info) as _;
            let mut driver_info = SP_DRVINFO_DATA_V2_W {
                cbSize: mem::size_of::<SP_DRVINFO_DATA_V2_W>() as u32,
                ..Default::default()
            };

            SetupDiEnumDriverInfoW(
                device_info_set,
                Some(&device_info_data),
                SPDIT_COMPATDRIVER,
                0,
                &mut driver_info,
            )?;

            log_info!("Selected driver version: {}", driver_info.DriverVersion);

            SetupDiSetSelectedDriverW(
                device_info_set,
                Some(&mut device_info_data),
                Some(&mut driver_info),
            )?;

            // Driver info list will be cleaned up automatically

            log_info!("Registering device...");

            SetupDiCallClassInstaller(
                DIF_REGISTERDEVICE,
                device_info_set,
                Some(&device_info_data),
            )?;

            log_info!("Registering co-installers...");

            SetupDiCallClassInstaller(
                DIF_REGISTER_COINSTALLERS,
                device_info_set,
                Some(&device_info_data),
            )?;

            log_info!("Installing interfaces...");

            SetupDiCallClassInstaller(
                DIF_INSTALLINTERFACES,
                device_info_set,
                Some(&device_info_data),
            )?;

            log_info!("Installing device...");

            SetupDiCallClassInstaller(DIF_INSTALLDEVICE, device_info_set, Some(&device_info_data))?;

            log_info!("Device created successfully");

            // Get device instance ID for completion
            let mut device_instance_id_buffer = [0u16; 200];
            let ret =
                CM_Get_Device_IDW(device_info_data.DevInst, &mut device_instance_id_buffer, 0);
            if ret != CR_SUCCESS {
                let error = CM_MapCrToWin32Err(ret, 0);
                return Err(VelociTunError::WindowsApi(Error::from_win32()));
            }

            let device_instance_id = String::from_utf16_lossy(&device_instance_id_buffer)
                .trim_end_matches('\0')
                .to_string();

            // Wait for device stabilization
            std::thread::sleep(std::time::Duration::from_millis(1000));

            // Complete device setup using the common function
            Self::complete_device_setup(name, description, guid, &device_instance_id)
        }
    }

    fn find_interface_luid_by_device(
        _name: &str,
        _target_guid: &GUID,
    ) -> VelociTunResult<NET_LUID_LH> {
        use windows::Win32::NetworkManagement::IpHelper::*;

        unsafe {
            let mut size = 0;
            GetAdaptersAddresses(
                AF_UNSPEC.0 as u32,
                GET_ADAPTERS_ADDRESSES_FLAGS(0),
                None,
                None,
                &mut size,
            );

            let mut buffer = vec![0u8; size as usize];
            let adapters = buffer.as_mut_ptr() as *mut IP_ADAPTER_ADDRESSES_LH;

            let result = GetAdaptersAddresses(
                AF_UNSPEC.0 as u32,
                GET_ADAPTERS_ADDRESSES_FLAGS(0),
                None,
                Some(adapters),
                &mut size,
            );
            if result != 0 {
                return Err(VelociTunError::WindowsApi(
                    windows::core::Error::from_win32(),
                ));
            }

            let mut current = adapters;
            while !current.is_null() {
                let adapter = &*current;

                if !adapter.Description.is_null() {
                    let description = std::slice::from_raw_parts(
                        adapter.Description.0,
                        Self::wcslen_safe(adapter.Description.0),
                    );
                    let description_str = String::from_utf16_lossy(description);
                    log_info!("Found adapter: {}", description_str);
                    if description_str.contains("VelociTun") {
                        return Ok(adapter.Luid);
                    }
                }

                current = adapter.Next;
            }

            std::thread::sleep(std::time::Duration::from_secs(20));
            Err(VelociTunError::AdapterNotFound)
        }
    }

    unsafe fn wcslen_safe(s: *const u16) -> usize {
        if s.is_null() {
            return 0;
        }
        let mut len = 0;
        let mut ptr = s;
        while *ptr != 0 && len < 65536 {
            len += 1;
            ptr = ptr.add(1);
        }
        len
    }
}

impl Default for AdapterBuilder {
    fn default() -> Self {
        Self::new()
    }
}

impl Adapter {
    /// Get adapter name
    pub fn name(&self) -> &str {
        &self.name
    }

    /// Get tunnel type
    pub fn tunnel_type(&self) -> &str {
        &self.tunnel_type
    }

    /// Get adapter GUID
    pub fn guid(&self) -> &GUID {
        &self.guid
    }

    /// Get adapter LUID
    pub fn luid(&self) -> NET_LUID_LH {
        self.luid
    }

    /// Find an existing adapter by name
    pub fn find_by_name<S: AsRef<str>>(name: S) -> VelociTunResult<Self> {
        use windows::Win32::NetworkManagement::IpHelper::*;

        let name = name.as_ref();
        log_info!("Searching for adapter '{}'", name);

        unsafe {
            let mut size = 0;
            GetAdaptersAddresses(
                AF_UNSPEC.0 as u32,
                GET_ADAPTERS_ADDRESSES_FLAGS(0),
                None,
                None,
                &mut size,
            );

            let mut buffer = vec![0u8; size as usize];
            let adapters = buffer.as_mut_ptr() as *mut IP_ADAPTER_ADDRESSES_LH;

            let result = GetAdaptersAddresses(
                AF_UNSPEC.0 as u32,
                GET_ADAPTERS_ADDRESSES_FLAGS(0),
                None,
                Some(adapters),
                &mut size,
            );
            if result != 0 {
                return Err(VelociTunError::WindowsApi(
                    windows::core::Error::from_win32(),
                ));
            }

            let mut current = adapters;
            while !current.is_null() {
                let adapter = &*current;

                if !adapter.FriendlyName.is_null() {
                    let friendly_name = std::slice::from_raw_parts(
                        adapter.FriendlyName.0,
                        AdapterBuilder::wcslen_safe(adapter.FriendlyName.0),
                    );
                    let friendly_name_str = String::from_utf16_lossy(friendly_name);

                    if friendly_name_str == name {
                        let mut guid = GUID::default();
                        let result = ConvertInterfaceLuidToGuid(&adapter.Luid, &mut guid);
                        if result.is_err() {
                            return Err(VelociTunError::WindowsApi(
                                windows::core::Error::from_win32(),
                            ));
                        }

                        return Ok(Adapter {
                            luid: adapter.Luid,
                            name: name.to_string(),
                            tunnel_type: "VelociTun".to_string(),
                            guid,
                        });
                    }
                }

                current = adapter.Next;
            }

            Err(VelociTunError::AdapterNotFound)
        }
    }

    /// List all VelociTun adapters
    pub fn list_all() -> VelociTunResult<Vec<Self>> {
        let mut adapters = Vec::new();

        unsafe {
            let mut size = 0;
            GetAdaptersAddresses(
                AF_UNSPEC.0 as u32,
                GET_ADAPTERS_ADDRESSES_FLAGS(0),
                None,
                None,
                &mut size,
            );

            let mut buffer = vec![0u8; size as usize];
            let adapter_addrs = buffer.as_mut_ptr() as *mut IP_ADAPTER_ADDRESSES_LH;

            let result = GetAdaptersAddresses(
                AF_UNSPEC.0 as u32,
                GET_ADAPTERS_ADDRESSES_FLAGS(0),
                None,
                Some(adapter_addrs),
                &mut size,
            );
            if result != 0 {
                return Err(VelociTunError::WindowsApi(
                    windows::core::Error::from_win32(),
                ));
            }

            let mut current = adapter_addrs;
            while !current.is_null() {
                let adapter = &*current;

                if !adapter.Description.is_null() {
                    let description = std::slice::from_raw_parts(
                        adapter.Description.0,
                        AdapterBuilder::wcslen_safe(adapter.Description.0),
                    );
                    let description_str = String::from_utf16_lossy(description);

                    if description_str.contains("VelociTun") {
                        let name = if !adapter.FriendlyName.is_null() {
                            let friendly_name = std::slice::from_raw_parts(
                                adapter.FriendlyName.0,
                                AdapterBuilder::wcslen_safe(adapter.FriendlyName.0),
                            );
                            String::from_utf16_lossy(friendly_name)
                        } else {
                            "Unknown".to_string()
                        };

                        let mut guid = GUID::default();
                        let _ = ConvertInterfaceLuidToGuid(&adapter.Luid, &mut guid);

                        adapters.push(Adapter {
                            luid: adapter.Luid,
                            name,
                            tunnel_type: "VelociTun".to_string(),
                            guid,
                        });
                    }
                }

                current = adapter.Next;
            }
        }

        Ok(adapters)
    }

    /// Create a new session for this adapter
    pub fn start_session(&self, capacity: u32) -> VelociTunResult<crate::session::Session> {
        crate::session::Session::new(std::sync::Arc::new(self.clone()), capacity)
    }
}

impl Drop for Adapter {
    fn drop(&mut self) {
        log_info!("Adapter '{}' being dropped", self.name);
    }
}

// Implement Clone for Adapter to enable sharing across sessions
impl Clone for Adapter {
    fn clone(&self) -> Self {
        Self {
            luid: self.luid,
            name: self.name.clone(),
            tunnel_type: self.tunnel_type.clone(),
            guid: self.guid,
            cfg_instance_id: self.cfg_instance_id,
            luid_index: self.luid_index,
            if_type: self.if_type,
            device_instance_id: self.device_instance_id.clone(),
            interface_filename: self.interface_filename.clone(),
        }
    }
}
