[package]
name = "velocitun"
version = "0.1.0"
edition = "2021"
authors = ["VelociTun Team"]
description = "High-performance TUN driver for Windows built with Rust"
license = "GPL-2.0"
readme = "README.md"
repository = "https://github.com/example/velocitun"
keywords = ["windows", "tunnel", "network", "driver", "vpn"]
categories = ["network-programming", "os::windows-apis"]

[lib]
name = "velocitun"
crate-type = ["cdylib", "staticlib", "rlib"]

[dependencies]
windows = { version = "0.58", features = [
    "Win32_Foundation",
    "Win32_System_SystemInformation",
    "Win32_System_ProcessStatus",
    "Win32_System_LibraryLoader",
    "Win32_System_Threading",
    "Win32_System_Memory",
    "Win32_System_IO",
    "Win32_System_Registry",
    "Win32_System_Services",
    "Win32_NetworkManagement_IpHelper",
    "Win32_NetworkManagement_Ndis",
    "Win32_Networking_WinSock",
    "Win32_Security",
    "Win32_Storage_FileSystem",
    "Win32_Devices_DeviceAndDriverInstallation",
    "Win32_System_Diagnostics_Debug",
    "Win32_Storage_InstallableFileSystems",
    "Win32_Devices_Enumeration_Pnp",
    "Win32_Devices_Properties",
    "Win32_Devices_DeviceQuery",
    "Win32_System_Com",
] }
log = "0.4"
thiserror = "1.0"
bitflags = "2.4"
uuid = { version = "1.6", features = ["v4"] }
tokio = { version = "1.0", features = ["full"], optional = true }
futures = { version = "0.3", optional = true }
scopeguard = "1.2.0"

[build-dependencies]
cc = "1.0"

[dev-dependencies]
env_logger = "0.10"

[features]
default = []
async = ["tokio", "futures"]
arm64-driver = []            # Enable ARM64 driver support

[profile.release]
panic = "abort"
lto = true
codegen-units = 1
opt-level = "s"
strip = true

[profile.dev]
panic = "abort"
opt-level = 0

[package.metadata.docs.rs]
targets = ["x86_64-pc-windows-msvc"]
default-target = "x86_64-pc-windows-msvc"

[package.metadata.velocitun]
driver-version = "0.14.1"
minimum-os-version = "6.1" # Windows 7

[[example]]
name = "async_api"
path = "examples/async_api.rs"
required-features = ["async"]

[[example]]
name = "velocitun_test"
path = "examples/velocitun_test.rs"
