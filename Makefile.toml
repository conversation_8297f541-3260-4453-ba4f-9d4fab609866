# VelociTun Makefile for cargo-make
# Install with: cargo install cargo-make
# Usage: cargo make build-driver

[config]
default_to_workspace = false

[env]
MSBUILD_PATH = { script = ["powershell", "-Command", "Get-Command msbuild -ErrorAction SilentlyContinue | Select-Object -ExpandProperty Source"] }

[tasks.find-msbuild]
description = "Find MSBuild executable"
script_runner = "powershell"
script = '''
$msbuildPaths = @(
    "${env:ProgramFiles}\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles}\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe", 
    "${env:ProgramFiles}\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Enterprise\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"
)

foreach ($path in $msbuildPaths) {
    if (Test-Path $path) {
        Write-Host "Found MSBuild at: $path"
        $env:MSBUILD_PATH = $path
        break
    }
}

if (-not $env:MSBUILD_PATH) {
    Write-Error "MSBuild not found. Please install Visual Studio with C++ workload."
    exit 1
}
'''

[tasks.build-driver]
description = "Build VelociTun kernel driver"
dependencies = ["find-msbuild"]
script_runner = "powershell"
script = '''
if (-not $env:MSBUILD_PATH) {
    Write-Error "MSBuild path not set"
    exit 1
}

Write-Host "Building VelociTun driver..."
& "$env:MSBUILD_PATH" driver\velocitun.vcxproj /p:Configuration=Release /p:Platform=x64 /p:EnableInfVerif=false /p:SignMode=Off /verbosity:minimal

if ($LASTEXITCODE -eq 0) {
    Write-Host "Driver built successfully!"
    
    # Copy driver files to resources
    if (-not (Test-Path "resources")) {
        New-Item -ItemType Directory -Path "resources" -Force
    }
    
    $driverFiles = @(
        @{Source="driver\x64\Release\velocitun.sys"; Target="resources\velocitun-amd64.sys"},
        @{Source="driver\x64\Release\velocitun\velocitun.sys"; Target="resources\velocitun-amd64.sys"},
        @{Source="driver\x64\Release\velocitun\velocitun.inf"; Target="resources\velocitun.inf"},
        @{Source="driver\x64\Release\velocitun\velocitun.cat"; Target="resources\velocitun.cat"}
    )
    
    foreach ($file in $driverFiles) {
        if (Test-Path $file.Source) {
            Copy-Item $file.Source $file.Target -Force
            Write-Host "Copied $($file.Source) to $($file.Target)"
        }
    }
} else {
    Write-Error "Driver build failed"
    exit 1
}
'''

[tasks.clean-driver]
description = "Clean driver build artifacts"
script_runner = "powershell"
script = '''
Write-Host "Cleaning driver build artifacts..."

$pathsToClean = @(
    "driver\x64",
    "driver\Win32", 
    "driver\ARM64",
    "driver\*.log",
    "driver\*.wrn",
    "driver\*.err"
)

foreach ($path in $pathsToClean) {
    if (Test-Path $path) {
        Remove-Item $path -Recurse -Force
        Write-Host "Removed: $path"
    }
}

Write-Host "Driver artifacts cleaned"
'''

[tasks.build-all]
description = "Build both Rust project and driver"
dependencies = ["build-driver"]
command = "cargo"
args = ["build", "--release"]

[tasks.test-driver]
description = "Test if driver files exist and are valid"
script_runner = "powershell"
script = '''
$driverFiles = @(
    "resources\velocitun-amd64.sys",
    "resources\velocitun.inf",
    "resources\velocitun.cat"
)

$allExist = $true
foreach ($file in $driverFiles) {
    if (Test-Path $file) {
        $size = (Get-Item $file).Length
        Write-Host "✓ $file ($size bytes)"
    } else {
        Write-Host "✗ $file (missing)"
        $allExist = $false
    }
}

if ($allExist) {
    Write-Host "All driver files present!"
} else {
    Write-Error "Some driver files are missing. Run 'cargo make build-driver' first."
    exit 1
}
'''
