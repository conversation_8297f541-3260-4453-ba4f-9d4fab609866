/* SPDX-License-Identifier: GPL-2.0
 *
 * Copyright (C) 2018-2021 WireGuard LLC. All Rights Reserved.
 */

#include <windows.h>
#include <ntverp.h>

#pragma code_page(1252)

wintun.cat RCDATA "driver\\wintun.cat"
wintun.inf RCDATA "driver\\wintun.inf"
wintun.sys RCDATA "driver\\wintun.sys"

#if defined(WANT_AMD64_WOW64)
#    if defined(BUILT_AMD64_WOW64)
wintun-amd64.cat RCDATA "amd64\\driver\\wintun.cat"
wintun-amd64.inf RCDATA "amd64\\driver\\wintun.inf"
wintun-amd64.sys RCDATA "amd64\\driver\\wintun.sys"
setupapihost-amd64.dll RCDATA "amd64\\setupapihost.dll"
#    else
#        pragma message("AMD64 wintun.sys was not built, so this will not work from WOW64")
#    endif
#endif
#if defined(WANT_ARM64_WOW64)
#    if defined(BUILT_ARM64_WOW64)
wintun-arm64.cat RCDATA "arm64\\driver\\wintun.cat"
wintun-arm64.inf RCDATA "arm64\\driver\\wintun.inf"
wintun-arm64.sys RCDATA "arm64\\driver\\wintun.sys"
setupapihost-arm64.dll RCDATA "arm64\\setupapihost.dll"
#    else
#        pragma message("ARM64 wintun.sys was not built, so this will not work from WOW64")
#    endif
#endif

#define STRINGIZE(x) #x
#define EXPAND(x) STRINGIZE(x)

VS_VERSION_INFO VERSIONINFO
FILEVERSION    WINTUN_VERSION_MAJ, WINTUN_VERSION_MIN, WINTUN_VERSION_REL, 0
PRODUCTVERSION WINTUN_VERSION_MAJ, WINTUN_VERSION_MIN, WINTUN_VERSION_REL, 0
FILEOS         VOS_NT_WINDOWS32
FILETYPE       VFT_DLL
FILESUBTYPE    VFT2_UNKNOWN
BEGIN
  BLOCK "StringFileInfo"
  BEGIN
    BLOCK "040904b0"
    BEGIN
      VALUE "CompanyName", "WireGuard LLC"
      VALUE "FileDescription", "Wintun API Library"
      VALUE "FileVersion", EXPAND(WINTUN_VERSION)
      VALUE "InternalName", "wintun.dll"
      VALUE "LegalCopyright", "Copyright \xa9 2018-2021 WireGuard LLC. All Rights Reserved."
      VALUE "OriginalFilename", "wintun.dll"
      VALUE "ProductName", "Wintun Driver"
      VALUE "ProductVersion", EXPAND(WINTUN_VERSION)
      VALUE "Comments", "https://www.wintun.net/"
    END
  END
  BLOCK "VarFileInfo"
  BEGIN
    VALUE "Translation", 0x409, 1200
  END
END
