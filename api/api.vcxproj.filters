<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="resources.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="exports.def">
      <Filter>Source Files</Filter>
    </None>
    <None Include="nci.def">
      <Filter>Source Files</Filter>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="nci.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="namespace.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="registry.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="logger.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="resource.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="adapter.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="wintun.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="main.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ntdll.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="rundll32.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="driver.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="adapter_win7.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="namespace.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="rundll32.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="logger.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="resource.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="adapter.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="session.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="main.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="driver.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="registry.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
</Project>
